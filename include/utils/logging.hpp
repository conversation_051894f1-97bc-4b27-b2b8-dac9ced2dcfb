// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef UTILS_LOGGING_HPP_
#define UTILS_LOGGING_HPP_

#include "spdlog/sinks/rotating_file_sink.h"
#include "spdlog/spdlog.h"
#include <iostream>
#include <memory>
#include <string>

namespace aby_box {

class LogHandler {
public:
  LogHandler(const std::string &module_name) : module_name_(module_name) {
    init_logger();
  }
  template <typename... Args> void info(const Args &...args) {
    logger_->info("[{}] {}", module_name_, fmt::format(args...));
  }
  template <typename... Args> void warn(const Args &...args) {
    logger_->warn("[{}] {}", module_name_, fmt::format(args...));
  }
  template <typename... Args> void debug(const Args &...args) {
    logger_->debug("[{}] {}", module_name_, fmt::format(args...));
  }

  template <typename... Args> void error(const Args &...args) {
    logger_->error("[{}] {}", module_name_, fmt::format(args...));
  }

private:
  static void init_logger() {
    if (!logger_) {
      auto max_size = 1024 * 1024 * 50; // 50MB
      auto max_files = 3;              // 最多保留3个日志文件
      logger_ = spdlog::rotating_logger_mt(
          "aby_box", "/var/aby_box_log/aby_box.txt", max_size, max_files, false);
      logger_->set_level(spdlog::level::debug);
      spdlog::flush_every(std::chrono::seconds(3));
      logger_->flush_on(spdlog::level::debug);
      logger_->info("New session =================");
    }
  }

  static std::shared_ptr<spdlog::logger> logger_;
  std::string module_name_;
};
} // namespace aby_box

#endif // UTILS_LOGGING_HPP_