// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#pragma once

#include <mutex>
#include <shared_mutex>
#include <condition_variable>
#include <atomic>
#include <thread>
#include <chrono>
#include <unordered_map>
#include <unordered_set>
#include <memory>
#include <functional>
#include <queue>
#include "common/loggable.hpp"

namespace aby_box {

// Thread-safe counter
class ThreadSafeCounter {
public:
  ThreadSafeCounter(int initial_value = 0) : value_(initial_value) {}
  
  int increment() { return ++value_; }
  int decrement() { return --value_; }
  int get() const { return value_.load(); }
  void set(int value) { value_ = value; }
  
  // Atomic operations
  int fetch_add(int value) { return value_.fetch_add(value); }
  int fetch_sub(int value) { return value_.fetch_sub(value); }
  
private:
  std::atomic<int> value_;
};

// Thread-safe flag
class ThreadSafeFlag {
public:
  ThreadSafeFlag(bool initial_value = false) : flag_(initial_value) {}
  
  void set() { flag_ = true; }
  void clear() { flag_ = false; }
  bool is_set() const { return flag_.load(); }
  
  // Test and set atomically
  bool test_and_set() { return flag_.exchange(true); }
  
  // Wait for flag to be set
  void wait_for_set(std::chrono::milliseconds timeout = std::chrono::milliseconds(0)) const {
    if (timeout == std::chrono::milliseconds(0)) {
      while (!flag_.load()) {
        std::this_thread::yield();
      }
    } else {
      auto start = std::chrono::steady_clock::now();
      while (!flag_.load() && 
             (std::chrono::steady_clock::now() - start) < timeout) {
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
      }
    }
  }
  
private:
  std::atomic<bool> flag_;
};

// Deadlock detection helper
class DeadlockDetector : public Loggable {
public:
  static DeadlockDetector& getInstance();
  
  // Register mutex acquisition
  void registerLockAcquisition(const void* mutex_ptr, const std::string& location);
  void registerLockRelease(const void* mutex_ptr);
  
  // Check for potential deadlocks
  bool checkForDeadlock();
  
  // Enable/disable deadlock detection
  void enable() { enabled_ = true; }
  void disable() { enabled_ = false; }
  
private:
  DeadlockDetector();
  
  struct LockInfo {
    std::thread::id thread_id;
    std::string location;
    std::chrono::steady_clock::time_point acquisition_time;
  };
  
  std::atomic<bool> enabled_{false};
  std::unordered_map<const void*, LockInfo> active_locks_;
  std::unordered_map<std::thread::id, std::unordered_set<const void*>> thread_locks_;
  mutable std::mutex detector_mutex_;
};

// RAII lock guard with deadlock detection
template<typename Mutex>
class SafeLockGuard {
public:
  explicit SafeLockGuard(Mutex& mutex, const std::string& location = "unknown")
    : mutex_(mutex), location_(location) {
    
    auto& detector = DeadlockDetector::getInstance();
    detector.registerLockAcquisition(&mutex_, location_);
    
    mutex_.lock();
    locked_ = true;
  }
  
  ~SafeLockGuard() {
    if (locked_) {
      mutex_.unlock();
      auto& detector = DeadlockDetector::getInstance();
      detector.registerLockRelease(&mutex_);
    }
  }
  
  // Non-copyable
  SafeLockGuard(const SafeLockGuard&) = delete;
  SafeLockGuard& operator=(const SafeLockGuard&) = delete;
  
private:
  Mutex& mutex_;
  std::string location_;
  bool locked_ = false;
};

// Thread pool with proper shutdown
class ThreadPool : public Loggable {
public:
  explicit ThreadPool(size_t num_threads, const std::string& name = "ThreadPool");
  ~ThreadPool();
  
  // Submit a task to the thread pool
  template<typename F>
  void submit(F&& task) {
    {
      std::lock_guard<std::mutex> lock(queue_mutex_);
      if (shutdown_) {
        log_handler_->warn("Cannot submit task to shutdown thread pool");
        return;
      }
      tasks_.emplace(std::forward<F>(task));
    }
    condition_.notify_one();
  }
  
  // Graceful shutdown
  void shutdown(std::chrono::milliseconds timeout = std::chrono::seconds(30));
  
  // Get pool statistics
  size_t getActiveThreads() const { return active_threads_.load(); }
  size_t getQueuedTasks() const {
    std::lock_guard<std::mutex> lock(queue_mutex_);
    return tasks_.size();
  }
  
private:
  void workerLoop();
  
  std::vector<std::thread> workers_;
  std::queue<std::function<void()>> tasks_;
  
  mutable std::mutex queue_mutex_;
  std::condition_variable condition_;
  std::atomic<bool> shutdown_{false};
  std::atomic<size_t> active_threads_{0};
  
  std::string pool_name_;
};

// Thread-safe singleton template
template<typename T>
class ThreadSafeSingleton {
public:
  static T& getInstance() {
    std::call_once(init_flag_, []() {
      instance_ = std::make_unique<T>();
    });
    return *instance_;
  }
  
  // Delete copy and move constructors
  ThreadSafeSingleton(const ThreadSafeSingleton&) = delete;
  ThreadSafeSingleton& operator=(const ThreadSafeSingleton&) = delete;
  ThreadSafeSingleton(ThreadSafeSingleton&&) = delete;
  ThreadSafeSingleton& operator=(ThreadSafeSingleton&&) = delete;
  
protected:
  ThreadSafeSingleton() = default;
  virtual ~ThreadSafeSingleton() = default;
  
private:
  static std::unique_ptr<T> instance_;
  static std::once_flag init_flag_;
};

template<typename T>
std::unique_ptr<T> ThreadSafeSingleton<T>::instance_ = nullptr;

template<typename T>
std::once_flag ThreadSafeSingleton<T>::init_flag_;

// Macros for easier usage
#define SAFE_LOCK(mutex) aby_box::SafeLockGuard<decltype(mutex)> lock(mutex, __FILE__ ":" + std::to_string(__LINE__))
#define SAFE_LOCK_NAMED(mutex, name) aby_box::SafeLockGuard<decltype(mutex)> lock(mutex, name)

// Thread naming utility
class ThreadNamer {
public:
  static void setThreadName(const std::string& name);
  static std::string getThreadName();
  static void setThreadName(std::thread& thread, const std::string& name);
};

} // namespace aby_box
