// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#pragma once

#include <string>
#include <fstream>
#include <filesystem>
#include <functional>
#include <optional>
#include <vector>
#include <mutex>
#include <chrono>
#include "common/loggable.hpp"

namespace aby_box {

// File operation result
enum class FileOpResult {
  SUCCESS,
  FILE_NOT_FOUND,
  PERMISSION_DENIED,
  DISK_FULL,
  IO_ERROR,
  INVALID_PATH,
  TIMEOUT,
  UNKNOWN_ERROR
};

// Safe file operations utility
class SafeFileOps : public Loggable {
public:
  SafeFileOps();
  
  // Atomic file operations
  FileOpResult writeFileAtomic(const std::string& path, const std::string& content);
  FileOpResult writeFileAtomic(const std::string& path, const std::vector<uint8_t>& data);
  
  std::optional<std::string> readFileContent(const std::string& path);
  std::optional<std::vector<uint8_t>> readFileBinary(const std::string& path);
  
  // Safe file copy with verification
  FileOpResult copyFile(const std::string& source, const std::string& destination, 
                       bool verify_checksum = true);
  
  // Safe file move (atomic if on same filesystem)
  FileOpResult moveFile(const std::string& source, const std::string& destination);
  
  // Directory operations
  FileOpResult createDirectories(const std::string& path);
  FileOpResult removeDirectory(const std::string& path, bool recursive = false);
  
  // File existence and properties
  bool fileExists(const std::string& path) const;
  bool isDirectory(const std::string& path) const;
  bool isWritable(const std::string& path) const;
  std::optional<size_t> getFileSize(const std::string& path) const;
  
  // Backup and restore
  FileOpResult createBackup(const std::string& path, const std::string& backup_suffix = ".bak");
  FileOpResult restoreFromBackup(const std::string& path, const std::string& backup_suffix = ".bak");
  
  // File locking for concurrent access
  class FileLock {
  public:
    FileLock(const std::string& path);
    ~FileLock();
    
    bool tryLock(std::chrono::milliseconds timeout = std::chrono::milliseconds(5000));
    void unlock();
    bool isLocked() const { return locked_; }
    
  private:
    std::string lock_path_;
    int fd_ = -1;
    bool locked_ = false;
  };
  
  // Temporary file management
  class TempFile {
  public:
    TempFile(const std::string& prefix = "aby_temp", const std::string& suffix = ".tmp");
    ~TempFile();
    
    const std::string& getPath() const { return path_; }
    std::fstream& getStream() { return stream_; }
    
    // Convert to permanent file
    FileOpResult moveTo(const std::string& destination);
    
  private:
    std::string path_;
    std::fstream stream_;
  };
  
  // Configuration file operations with validation
  template<typename T>
  FileOpResult writeConfig(const std::string& path, const T& config, 
                          std::function<bool(const T&)> validator = nullptr);
  
  template<typename T>
  std::optional<T> readConfig(const std::string& path, 
                             std::function<T(const std::string&)> parser,
                             std::function<bool(const T&)> validator = nullptr);
  
  // Disk space management
  std::optional<size_t> getAvailableSpace(const std::string& path) const;
  bool hasEnoughSpace(const std::string& path, size_t required_bytes) const;
  
  // File integrity verification
  std::optional<std::string> calculateChecksum(const std::string& path, 
                                              const std::string& algorithm = "md5") const;
  bool verifyChecksum(const std::string& path, const std::string& expected_checksum,
                     const std::string& algorithm = "md5") const;
  
  // Error handling
  static std::string getErrorString(FileOpResult result);
  
private:
  std::string generateTempPath(const std::string& prefix, const std::string& suffix) const;
  FileOpResult handleFileSystemError(const std::filesystem::filesystem_error& e) const;
  FileOpResult handleStdError(const std::exception& e) const;
  
  mutable std::mutex ops_mutex_;
  static constexpr size_t MAX_RETRY_COUNT = 3;
  static constexpr std::chrono::milliseconds RETRY_DELAY{100};
};

// Global safe file operations instance
SafeFileOps& getSafeFileOps();

// Convenience macros
#define SAFE_WRITE_FILE(path, content) \
  aby_box::getSafeFileOps().writeFileAtomic(path, content)

#define SAFE_READ_FILE(path) \
  aby_box::getSafeFileOps().readFileContent(path)

#define SAFE_COPY_FILE(src, dst) \
  aby_box::getSafeFileOps().copyFile(src, dst, true)

#define SAFE_MOVE_FILE(src, dst) \
  aby_box::getSafeFileOps().moveFile(src, dst)

} // namespace aby_box
