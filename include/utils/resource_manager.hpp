// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#pragma once

#include <memory>
#include <functional>
#include <fstream>
#include <mutex>
#include <unordered_map>
#include <vector>
#include <chrono>
#include <atomic>
#include "common/loggable.hpp"

namespace aby_box {

// RAII wrapper for file handles
class FileHandle {
public:
  FileHandle() = default;
  explicit FileHandle(const std::string& path, std::ios::openmode mode = std::ios::in);
  ~FileHandle();
  
  // Move semantics
  FileHandle(FileHandle&& other) noexcept;
  FileHandle& operator=(FileHandle&& other) noexcept;
  
  // Delete copy semantics
  FileHandle(const FileHandle&) = delete;
  FileHandle& operator=(const FileHandle&) = delete;
  
  bool is_open() const;
  std::fstream& get();
  void close();
  
private:
  std::unique_ptr<std::fstream> file_;
};

// RAII wrapper for memory allocations
template<typename T>
class MemoryBlock {
public:
  explicit MemoryBlock(size_t count = 1) 
    : ptr_(static_cast<T*>(std::aligned_alloc(alignof(T), sizeof(T) * count)))
    , count_(count) {
    if (!ptr_) {
      throw std::bad_alloc();
    }
  }
  
  ~MemoryBlock() {
    if (ptr_) {
      std::free(ptr_);
    }
  }
  
  // Move semantics
  MemoryBlock(MemoryBlock&& other) noexcept 
    : ptr_(other.ptr_), count_(other.count_) {
    other.ptr_ = nullptr;
    other.count_ = 0;
  }
  
  MemoryBlock& operator=(MemoryBlock&& other) noexcept {
    if (this != &other) {
      if (ptr_) {
        std::free(ptr_);
      }
      ptr_ = other.ptr_;
      count_ = other.count_;
      other.ptr_ = nullptr;
      other.count_ = 0;
    }
    return *this;
  }
  
  // Delete copy semantics
  MemoryBlock(const MemoryBlock&) = delete;
  MemoryBlock& operator=(const MemoryBlock&) = delete;
  
  T* get() const { return ptr_; }
  size_t size() const { return count_; }
  T& operator[](size_t index) { return ptr_[index]; }
  const T& operator[](size_t index) const { return ptr_[index]; }
  
private:
  T* ptr_ = nullptr;
  size_t count_ = 0;
};

// Resource cleanup manager
class ResourceManager : public Loggable {
public:
  static ResourceManager& getInstance();
  
  // Register cleanup functions
  void registerCleanup(const std::string& name, std::function<void()> cleanup_func);
  void unregisterCleanup(const std::string& name);
  
  // Execute all cleanup functions
  void cleanupAll();
  
  // Memory monitoring
  void startMemoryMonitoring(std::chrono::milliseconds interval = std::chrono::seconds(30));
  void stopMemoryMonitoring();
  
  // Get memory usage statistics
  struct MemoryStats {
    size_t total_memory_kb = 0;
    size_t available_memory_kb = 0;
    size_t used_memory_kb = 0;
    size_t process_memory_kb = 0;
    double memory_usage_percent = 0.0;
  };
  
  MemoryStats getMemoryStats() const;
  
  // File descriptor monitoring
  void startFdMonitoring(std::chrono::milliseconds interval = std::chrono::seconds(60));
  void stopFdMonitoring();
  int getCurrentFdCount() const;
  
  // Emergency cleanup (for critical situations)
  void emergencyCleanup();
  
private:
  ResourceManager();
  ~ResourceManager();
  
  void memoryMonitorLoop();
  void fdMonitorLoop();
  bool checkMemoryPressure() const;
  void handleMemoryPressure();
  
  std::unordered_map<std::string, std::function<void()>> cleanup_functions_;
  mutable std::mutex cleanup_mutex_;
  
  std::atomic<bool> memory_monitoring_active_{false};
  std::atomic<bool> fd_monitoring_active_{false};
  std::unique_ptr<std::thread> memory_monitor_thread_;
  std::unique_ptr<std::thread> fd_monitor_thread_;
  
  // Memory pressure thresholds
  static constexpr double MEMORY_WARNING_THRESHOLD = 80.0;  // 80%
  static constexpr double MEMORY_CRITICAL_THRESHOLD = 90.0; // 90%
  static constexpr int FD_WARNING_THRESHOLD = 800;          // 800 file descriptors
};

// Scoped resource guard
template<typename Resource>
class ScopedResource {
public:
  template<typename... Args>
  ScopedResource(std::function<void(Resource&)> cleanup, Args&&... args)
    : resource_(std::forward<Args>(args)...)
    , cleanup_(cleanup) {}
  
  ~ScopedResource() {
    if (cleanup_) {
      cleanup_(resource_);
    }
  }
  
  Resource& get() { return resource_; }
  const Resource& get() const { return resource_; }
  
  // Release ownership
  Resource release() {
    cleanup_ = nullptr;
    return std::move(resource_);
  }
  
private:
  Resource resource_;
  std::function<void(Resource&)> cleanup_;
};

// Helper macros for resource management
#define REGISTER_CLEANUP(name, func) \
  ResourceManager::getInstance().registerCleanup(name, func)

#define UNREGISTER_CLEANUP(name) \
  ResourceManager::getInstance().unregisterCleanup(name)

#define SCOPED_FILE(var, path, mode) \
  auto var = FileHandle(path, mode)

#define SCOPED_MEMORY(type, var, count) \
  auto var = MemoryBlock<type>(count)

} // namespace aby_box
