#ifndef CONFIG_MANAGER_HPP
#define CONFIG_MANAGER_HPP

#include "utils/logging.hpp"
#include <fstream>
#include <memory>
#include <mutex>
#include <nlohmann/json.hpp>
#include <string>
#include <sstream>
#include <cstdlib>
namespace aby_box {

// 前向声明
class Wifi;

class ConfigManager {
public:
  static ConfigManager &getInstance() {
    static ConfigManager instance;
    return instance;
  }

  bool init(const std::string &config_path = "/etc/cfg/aby_box/config.json") {
    std::lock_guard<std::mutex> lock(mutex_);
    config_path_ = config_path;  // 保存配置文件路径
    std::ifstream file(config_path);
    if (!file.is_open()) {
      log_handler_->error("Failed to open config file: {}", config_path);
      return false;
    }
    config_ = nlohmann::json::parse(file);
    log_handler_->info("Successfully loaded config from {}", config_path);
    return true;
  }

  // API相关配置获取方法
  std::string getBaseUrl() const {
    return getConfigValue<std::string>("data_upload_api.base_url", "");
  }

  std::string getmediamtxUrl() const {
    return getConfigValue<std::string>("mediamtx_api.base_url", "");
  }

  std::string getAuthToken() const {
    return getConfigValue<std::string>("data_upload_api.auth_token", "");
  }
  
  // 通用配置获取方法
  template <typename T>
  T getConfigValue(const std::string &path, const T &default_value) const {
    std::lock_guard<std::mutex> lock(mutex_);
    nlohmann::json::json_pointer ptr(convertToJsonPointer(path));
    return config_.value(ptr, default_value);
  }

  // 通用配置设置方法
  template <typename T>
  bool setConfigValue(const std::string &path, const T &value) {
    std::lock_guard<std::mutex> lock(mutex_);
    try {
      nlohmann::json::json_pointer ptr(convertToJsonPointer(path));
      config_[ptr] = value;
      return saveConfig();
    } catch (const std::exception &e) {
      log_handler_->error("Failed to set config value '{}': {}", path, e.what());
      return false;
    }
  }

  // WiFi配置相关方法
  std::string getWifiSSID() const {
    return getConfigValue<std::string>("wifi.ssid", "");
  }

  std::string getWifiPassword() const {
    return getConfigValue<std::string>("wifi.password", "");
  }

  bool isWifiConfigValid() const {
    return getConfigValue<bool>("wifi.is_valid", false);
  }

  bool setWifiConfig(const std::string &ssid, const std::string &password) {
    std::lock_guard<std::mutex> lock(mutex_);
    try {
      config_["wifi"]["ssid"] = ssid;
      config_["wifi"]["password"] = password;
      config_["wifi"]["is_valid"] = true;
      
      // 先保存配置到JSON文件
      if (!saveConfig()) {
        log_handler_->error("Failed to save WiFi config to JSON file");
        return false;
      }
      
      // 使用WiFi模块连接网络
      if (connectUsingWifiModule(ssid, password)) {
        log_handler_->info("WiFi configuration updated and connected: SSID={}", ssid);
        return true;
      } else {
        log_handler_->error("Failed to connect using WiFi module");
        return false;
      }
    } catch (const std::exception &e) {
      log_handler_->error("Failed to set WiFi config: {}", e.what());
      return false;
    }
  }

  // user_id相关方法
  std::string getUserId() const {
    return getConfigValue<std::string>("user_id", "");
  }

  bool setUserId(const std::string &user_id) {
    if (user_id.empty()) {
      log_handler_->warn("Attempted to set empty user ID");
      return false;
    }
    
    std::string current_user_id = getUserId();
    if (user_id != current_user_id) {
      log_handler_->info("Updating user ID from '{}' to '{}'", current_user_id, user_id);
      return setConfigValue("user_id", user_id);
    } else {
      log_handler_->debug("User ID unchanged, skipping update");
      return true;
    }
  }

  // 设备信息相关方法
  std::string getHardwareSn() const {
    return getConfigValue<std::string>("hardware_sn", "");
  }

  std::string getDeviceId() const {
    return getConfigValue<std::string>("device_id", "");
  }

  std::string getVersionId() const {
    return getConfigValue<std::string>("version_id", "");
  }

  bool setHardwareSn(const std::string &hardware_sn) {
    return setConfigValue("hardware_sn", hardware_sn);
  }

  bool setDeviceId(const std::string &device_id) {
    return setConfigValue("device_id", device_id);
  }

  bool setVersionId(const std::string &version_id) {
    return setConfigValue("version_id", version_id);
  }

  // Enhanced configuration management
  bool validateConfig() const;
  bool createBackup() const;
  bool restoreFromBackup(const std::string& backup_path);
  std::vector<std::string> listBackups() const;

  // Configuration integrity
  bool verifyConfigIntegrity() const;
  bool repairConfig();

  // Configuration versioning
  std::string getConfigVersion() const;
  bool upgradeConfig(const std::string& from_version, const std::string& to_version);

private:
  ConfigManager()
      : log_handler_(std::make_shared<LogHandler>("ConfigManager")) {}
  ~ConfigManager() = default;

  ConfigManager(const ConfigManager &) = delete;
  ConfigManager &operator=(const ConfigManager &) = delete;

  static std::string convertToJsonPointer(const std::string &path) {
    std::string result = "/" + path;
    size_t pos = 0;
    while ((pos = result.find('.', pos)) != std::string::npos) {
      result.replace(pos, 1, "/");
      pos++;
    }
    return result;
  }

  // 保存配置到文件
  bool saveConfig() {
    try {
      std::ofstream config_file(config_path_);
      if (!config_file.is_open()) {
        log_handler_->error("Failed to open config file for writing: {}", config_path_);
        return false;
      }
      
      config_file << config_.dump(2); // 使用2个空格缩进格式化
      config_file.close();
      
      log_handler_->debug("Configuration saved to {}", config_path_);
      return true;
    } catch (const std::exception &e) {
      log_handler_->error("Failed to save config: {}", e.what());
      return false;
    }
  }

  // 使用WiFi模块连接网络
  bool connectUsingWifiModule(const std::string &ssid, const std::string &password);

  // Helper methods for enhanced configuration management
  void cleanupOldBackups() const;
  bool createDefaultConfig();

  mutable std::mutex mutex_;
  nlohmann::json config_;
  std::shared_ptr<LogHandler> log_handler_;
  std::string config_path_ = "/etc/cfg/aby_box/config.json";
};

} // namespace aby_box

#endif // CONFIG_MANAGER_HPP