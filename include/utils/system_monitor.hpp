// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#pragma once

#include <string>
#include <vector>
#include <unordered_map>
#include <chrono>
#include <atomic>
#include <mutex>
#include <thread>
#include <functional>
#include <memory>
#include <optional>
#include "common/loggable.hpp"

namespace aby_box {

// System metrics structure
struct SystemMetrics {
  // CPU metrics
  double cpu_usage_percent = 0.0;
  double load_average_1min = 0.0;
  double load_average_5min = 0.0;
  double load_average_15min = 0.0;
  
  // Memory metrics
  size_t total_memory_kb = 0;
  size_t available_memory_kb = 0;
  size_t used_memory_kb = 0;
  double memory_usage_percent = 0.0;
  
  // Process metrics
  size_t process_memory_kb = 0;
  double process_cpu_percent = 0.0;
  int thread_count = 0;
  int fd_count = 0;
  
  // Disk metrics
  size_t disk_total_kb = 0;
  size_t disk_available_kb = 0;
  size_t disk_used_kb = 0;
  double disk_usage_percent = 0.0;
  
  // Network metrics
  bool network_connected = false;
  std::string network_interface = "";
  
  // Temperature (if available)
  double cpu_temperature = 0.0;
  
  // Timestamp
  std::chrono::system_clock::time_point timestamp;
};

// OTA-specific monitoring
struct OtaMetrics {
  bool ota_in_progress = false;
  std::string ota_version = "";
  std::string ota_status = "";
  double ota_progress_percent = 0.0;
  std::chrono::system_clock::time_point ota_start_time;
  std::chrono::milliseconds ota_duration{0};
  
  // Network stability during OTA
  bool network_stable = false;
  int network_interruptions = 0;
  
  // Resource usage during OTA
  double peak_memory_usage = 0.0;
  double peak_cpu_usage = 0.0;
};

// Alert levels
enum class AlertLevel {
  INFO,
  WARNING,
  CRITICAL,
  EMERGENCY
};

// System alert
struct SystemAlert {
  AlertLevel level;
  std::string category;
  std::string message;
  std::string details;
  std::chrono::system_clock::time_point timestamp;
  bool acknowledged = false;
};

// System monitor class
class SystemMonitor : public Loggable {
public:
  static SystemMonitor& getInstance();
  
  // Start/stop monitoring
  void startMonitoring(std::chrono::milliseconds interval = std::chrono::seconds(30));
  void stopMonitoring();
  
  // Get current metrics
  SystemMetrics getCurrentMetrics() const;
  OtaMetrics getCurrentOtaMetrics() const;
  
  // Alert management
  void addAlert(AlertLevel level, const std::string& category, 
               const std::string& message, const std::string& details = "");
  std::vector<SystemAlert> getUnacknowledgedAlerts() const;
  void acknowledgeAlert(size_t alert_id);
  void clearOldAlerts(std::chrono::hours max_age = std::chrono::hours(24));
  
  // OTA monitoring
  void startOtaMonitoring(const std::string& version);
  void updateOtaProgress(double progress_percent, const std::string& status);
  void stopOtaMonitoring(bool success);
  
  // Health checks
  bool isSystemHealthy() const;
  std::vector<std::string> getHealthIssues() const;
  
  // Performance tracking
  void recordPerformanceMetric(const std::string& name, double value);
  std::optional<double> getAveragePerformanceMetric(const std::string& name, 
                                                   std::chrono::minutes window = std::chrono::minutes(5)) const;
  
  // Log analysis for OTA issues
  std::vector<std::string> analyzeLogsForOtaIssues() const;
  void generateOtaReport(const std::string& output_path) const;
  
  // Callbacks for critical events
  void setOtaFailureCallback(std::function<void(const std::string&)> callback);
  void setSystemCriticalCallback(std::function<void(const SystemAlert&)> callback);
  
private:
  SystemMonitor();
  ~SystemMonitor();
  
  void monitoringLoop();
  SystemMetrics collectSystemMetrics();
  void checkSystemHealth(const SystemMetrics& metrics);
  void checkOtaHealth();
  
  // Metric collection helpers
  double getCpuUsage();
  void getLoadAverages(double& load1, double& load5, double& load15);
  void getMemoryInfo(size_t& total, size_t& available, size_t& used);
  void getProcessInfo(size_t& memory, double& cpu, int& threads, int& fds);
  void getDiskInfo(size_t& total, size_t& available, size_t& used);
  bool getNetworkStatus(std::string& interface);
  double getCpuTemperature();
  
  // Alert helpers
  void triggerAlert(const SystemAlert& alert);
  
  std::atomic<bool> monitoring_active_{false};
  std::unique_ptr<std::thread> monitoring_thread_;
  
  mutable std::mutex metrics_mutex_;
  SystemMetrics last_metrics_;
  OtaMetrics ota_metrics_;
  
  mutable std::mutex alerts_mutex_;
  std::vector<SystemAlert> alerts_;
  size_t next_alert_id_ = 0;
  
  mutable std::mutex performance_mutex_;
  std::unordered_map<std::string, std::vector<std::pair<std::chrono::steady_clock::time_point, double>>> performance_data_;
  
  // Callbacks
  std::function<void(const std::string&)> ota_failure_callback_;
  std::function<void(const SystemAlert&)> system_critical_callback_;
  
  // Health thresholds
  static constexpr double CPU_WARNING_THRESHOLD = 80.0;
  static constexpr double CPU_CRITICAL_THRESHOLD = 95.0;
  static constexpr double MEMORY_WARNING_THRESHOLD = 80.0;
  static constexpr double MEMORY_CRITICAL_THRESHOLD = 90.0;
  static constexpr double DISK_WARNING_THRESHOLD = 85.0;
  static constexpr double DISK_CRITICAL_THRESHOLD = 95.0;
  static constexpr double TEMPERATURE_WARNING_THRESHOLD = 70.0;
  static constexpr double TEMPERATURE_CRITICAL_THRESHOLD = 80.0;
  
  // OTA-specific thresholds
  static constexpr std::chrono::minutes OTA_TIMEOUT_WARNING{30};
  static constexpr std::chrono::minutes OTA_TIMEOUT_CRITICAL{60};
  static constexpr int MAX_NETWORK_INTERRUPTIONS = 3;
};

// Global system monitor instance
SystemMonitor& getSystemMonitor();

// Convenience macros for monitoring
#define MONITOR_PERFORMANCE(name, value) \
  aby_box::getSystemMonitor().recordPerformanceMetric(name, value)

#define SYSTEM_ALERT(level, category, message, details) \
  aby_box::getSystemMonitor().addAlert(aby_box::AlertLevel::level, category, message, details)

#define OTA_START_MONITORING(version) \
  aby_box::getSystemMonitor().startOtaMonitoring(version)

#define OTA_UPDATE_PROGRESS(progress, status) \
  aby_box::getSystemMonitor().updateOtaProgress(progress, status)

#define OTA_STOP_MONITORING(success) \
  aby_box::getSystemMonitor().stopOtaMonitoring(success)

} // namespace aby_box
