// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef MODULE__TEST_MODULE_HPP_
#define MODULE__TEST_MODULE_HPP_

#include "common/base_module.hpp"
#include "common/topics/cat_event.hpp"
#include "module/gpio_manager.hpp"
#include "utils/api_client.h"
#include "utils/utils.hpp"
#include <atomic>
#include <chrono>
#include <memory>
namespace aby_box {

/**
 * @brief 测试模块 - 用于手动测试图像捕获功能
 * 
 * 通过GPIO引脚的高电平信号触发CAT_ENTER事件，
 * 从而启动图像捕获过程进行测试
 */
class TestModule : public BaseModule {
public:
    explicit TestModule(const std::string &module_name);
    ~TestModule();
    
    bool init() override;
    bool start() override;
    bool stop() override;
    void join() override;
    
    // 设置GPIO管理器引用
    void set_gpio_manager(std::shared_ptr<GpioManager> gpio_manager);
    
private:
    void on_gpio_event(int pin, int value, uint64_t timestamp);
    void publish_cat_enter_event(uint64_t timestamp);
    void publish_cat_leave_event(uint64_t timestamp);
    void handle_cat_enter(uint64_t timestamp);
    void handle_cat_leave(uint64_t timestamp);
    
private:
    // GPIO管理器
    std::shared_ptr<GpioManager> gpio_manager_;
    int gpio_pin_;  // GPIO引脚号
    
    // 事件发布
    uorb::PublicationData<uorb::msg::cat_event> pub_cat_event_;
    
    // 防止频繁触发
    std::atomic<uint64_t> last_event_time_;
    static constexpr uint64_t MIN_EVENT_INTERVAL_US = 2000000;  // 2秒最小间隔，避免传感器抖动
    
    // 状态跟踪
    std::atomic<bool> cat_in_box_;
    std::atomic<bool> is_running_;
};

} // namespace aby_box

#endif // MODULE__TEST_MODULE_HPP_ 