#ifndef SENSOR__LTR303_HPP_
#define SENSOR__LTR303_HPP_

#include <stdint.h>

struct ltr303_data {
    uint16_t ch0;  // 可见光通道数据
    uint16_t ch1;  // 红外通道数据
};

/* IOCTL commands */
#define LTR303_IOC_MAGIC 'L'
#define LTR303_IOC_READ_DATA _IOR(LTR303_IOC_MAGIC, 1, struct ltr303_data *)
#define LTR303_IOC_SET_THRESHOLDS _IOW(LTR303_IOC_MAGIC, 2, struct ltr303_thresholds *)
#define LTR303_IOC_ENABLE_INT _IO(LTR303_IOC_MAGIC, 3)
#define LTR303_IOC_DISABLE_INT _IO(LTR303_IOC_MAGIC, 4)
#define LTR303_IOC_READ_INT_STATUS _IOR(LTR303_IOC_MAGIC, 5, int *)
#define LTR303_IOC_INIT_SENSOR _IO(LTR303_IOC_MAGIC, 6)

#endif // SENSOR__LTR303_HPP_