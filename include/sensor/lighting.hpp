// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef SENSOR__LIGHTING_HPP_
#define SENSOR__LIGHTING_HPP_

#include "common/base_module.hpp"
#include "sensor/ltr303.hpp"
#include <fcntl.h>
#include <fstream>
#include <json-c/json.h>
#include <sys/ioctl.h>
#include <vector>
#include <thread>

namespace aby_box {

class Lighting : public BaseModule {
public:
  explicit Lighting(const std::string &module_name);
  virtual ~Lighting();

  bool init() override;
  bool start() override;
  bool stop() override;
  void join() override;

private:
  void thread_publisher();
  
  bool is_running_{false};
  int fd_{-1};
  std::thread thread_publisher_;
};

} // namespace aby_box

#endif // SENSOR__LIGHTING_HPP_