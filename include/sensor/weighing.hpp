// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef SENSOR__WEIGHING_HPP_
#define SENSOR__WEIGHING_HPP_

#include "common/base_module.hpp"
#include "common/topics/sensor_weight.hpp"
#include "sensor/hx711.hpp"
#include <fcntl.h>
#include <fstream>
#include <json-c/json.h>
#include <sys/ioctl.h>
#include <complex>
#include <vector>
#include <deque>

namespace aby_box {

struct WeighingCalibration {
  float calibration_weight = 5000.0;
  int32_t offset = 0;
  float scale_factor = 0.0;
};

struct WeightFilter {
    std::vector<double> window;          
    size_t windowSize;       
    size_t currentSize;      
    double spikeThreshold;   
    double stableThreshold;  
    size_t minStableCount;   
    double lastValidValue;   
    bool isFirstFrame;       
    int consecutiveChanges;  
    double lastChange;       
};

class Weighing : public BaseModule {
public:
    explicit Weighing(const std::string &module_name,
                     bool enable_debug = false,
                     bool calibration_mode = false);
    virtual ~Weighing();

    // 声明必须实现的虚函数
    bool init() override;
    bool start() override;
    bool stop() override;
    void join() override;

    bool calibrate();

private:
    std::unique_ptr<WeightFilter> weight_filter_;
    bool is_running_{false};
    bool calibration_mode_{false};
    int fd_{-1};
    std::thread thread_publisher_;
    WeighingCalibration calib_;
    const std::string calib_file_ = "/etc/cfg/aby_box/weighing_calibration.json";

    // FFT相关参数
    static constexpr size_t FFT_WINDOW_SIZE = 64;    // FFT窗口大小
    static constexpr float SAMPLING_RATE = 10.0f;    // 采样率(Hz)
    static constexpr float MIN_FREQ = 0.2f;          // 最小频率(Hz)
    static constexpr float MAX_FREQ = 2.0f;          // 最大频率(Hz)
    static constexpr float POWER_THRESHOLD = 0.3f;   // 功率阈值

    // 滑动窗口滤波参数
    static constexpr size_t WINDOW_SIZE = 32;        // 滑动窗口大小
    static constexpr float SPIKE_THRESHOLD = 3.0f;   // 尖峰检测阈值
    static constexpr float TREND_RATIO = 0.6f;       // 趋势判定比例
    static constexpr float ALPHA = 0.1f;             // 指数平滑系数
    
    // FFT相关成员变量
    // std::vector<float> time_window_;                 // FFT时域数据
    // std::vector<std::complex<float>> fft_buffer_;    // FFT缓冲区
    float last_stable_value_{0.0f};                  // 最后的稳定值
    
    // 滑动窗口相关成员变量
    // std::deque<float> weight_window_;                // 滑动窗口
    float exp_avg_{0.0f};                           // 指数平滑平均值
    float last_valid_weight_{0.0f};                 // 上一个有效值
    
    // 新的滤波方法
    float filter_weight(float raw_weight);
    bool is_spike(float value, float mean, float std_dev) const;
    bool is_stable_trend(const std::deque<float>& window, float new_value) const;
    std::pair<float, float> calculate_stats(const std::deque<float>& values) const;
    
    void thread_publisher();
    bool load_calibration();
    bool save_calibration();

    // 新增方法
    void perform_fft();
    bool is_valid_signal(const std::vector<std::complex<float>>& spectrum);
    std::vector<float> apply_bandpass_filter(const std::vector<std::complex<float>>& spectrum);
    float process_weight_signal(float raw_weight);

    // Add new helper function declarations
    bool is_data_stable(const WeightFilter* filter, double mean);
    void add_to_window(WeightFilter* filter, double value);
    double calculate_mean(const std::vector<double>& data);
    double filter_weight_signal(double raw_weight);
};

} // namespace aby_box

#endif // SENSOR__WEIGHING_HPP_