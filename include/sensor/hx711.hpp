#ifndef SENSOR__HX711_HPP_
#define SENSOR__HX711_HPP_

#include <stdint.h>

/* IOCTL commands */
#define HX711_IOC_MAGIC 'h'
#define HX711_GET_READING _IOR(HX711_IOC_MAGIC, 1, int32_t *)
#define HX711_SET_GAIN _IOW(HX711_IOC_MAGIC, 2, uint8_t)
#define HX711_GET_GAIN _IOR(HX711_IOC_MAGIC, 3, uint8_t *)
#define HX711_CALI_OFFSET _IOR(HX711_IOC_MAGIC, 4, int32_t *)
#define HX711_CALI_FACTOR _IOR(HX711_IOC_MAGIC, 5, int32_t *)

/* HX711 gain options */
#define HX711_GAIN_128 1
#define HX711_GAIN_32 2
#define HX711_GAIN_64 3

/* Sample rate options */
#define HX711_RATE_10SPS 0
#define HX711_RATE_80SPS 1

#endif // SENSOR__HX711_HPP_
