#ifndef UTILS_MODULE_NAMES_HPP_
#define UTILS_MODULE_NAMES_HPP_

namespace aby_box {

// BOX_DETECTOR
constexpr const char *BOX_DETECTOR = "BoxDetector";

// VIDEO_ENGINE
constexpr const char *VIDEO_ENGINE = "VideoEngine";

// WEIGHT_ANALYZER
constexpr const char *WEIGHT_ANALYZER = "WeightAnalyzer";

constexpr const char *VIDEO_ENGINE_START_RECORD = "START_RECORD";
constexpr const char *VIDEO_ENGINE_STOP_RECORD = "STOP_RECORD";
// DISPLAYER
constexpr const char *DISPLAYER = "Displayer";

// SOUND
constexpr const char *SOUND = "Sound";

// DATA_PROCESSOR
constexpr const char *CATBEHAVIOR_MANAGER = "CatBehaviorManager";

// UPLOADER
constexpr const char *UPLOADER = "Uploader";

// WIFI
constexpr const char *WIFI = "Wifi";

constexpr const char *BROADCAST = "Broadcast";

// WEIGHING
constexpr const char *WEIGHING = "Weighing";

// STATISTICS
constexpr const char *STATISTICS = "Statistics";

// CLOUD_SYNC_MANAGER
constexpr const char *CLOUD_SYNC_MANAGER = "CloudSyncManager";

// OTA_MANAGER
constexpr const char *OTA_MANAGER = "OtaManager";

// LIGHTING
constexpr const char *LIGHTING = "Lighting";

// GPIO_MANAGER
constexpr const char *GPIO_MANAGER = "GpioManager";

// TEST_MODULE
constexpr const char *TEST_MODULE = "TestModule";

// BLUETOOTH_MANAGER
constexpr const char *BLUETOOTH_MANAGER = "BluetoothManager";

// INITIALIZATION_MANAGER
constexpr const char *INITIALIZATION_MANAGER = "InitializationManager";

} // namespace aby_box

#endif // UTILS_MODULE_NAMES_HPP_