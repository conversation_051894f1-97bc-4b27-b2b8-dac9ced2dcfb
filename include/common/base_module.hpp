// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef COMMON_BASE__BASE_MODULE_HPP_
#define COMMON_BASE__BASE_MODULE_HPP_

#include "common/loggable.hpp"
#include "common/macro.hpp"
#include "uorb/abs_time.h"
#include "uorb/publication.h"
#include "uorb/publication_multi.h"
#include "uorb/subscription.h"
#include "uorb/subscription_interval.h"
#include "utils/config_manager.hpp"
#include "utils/logging.hpp"
#include <functional>
#include <map>
#include <memory>
#include <string>
#include <atomic>
#include <mutex>
#include <chrono>
#include <condition_variable>

namespace aby_box {

// Module lifecycle states
enum class ModuleState {
  UNINITIALIZED,
  INITIALIZED,
  STARTING,
  RUNNING,
  STOPPING,
  STOPPED,
  ERROR
};

class BaseModule : public Loggable {
public:
  BaseModule(const std::string &module_name)
    : Loggable(module_name)
    , module_name_(module_name)
    , error_count_(0)
    , in_error_state_(false)
    , error_threshold_(10)
    , state_(ModuleState::UNINITIALIZED)
    , shutdown_requested_(false)
    , graceful_shutdown_timeout_(std::chrono::seconds(30)) {}

  virtual ~BaseModule() {
    if (state_ == ModuleState::RUNNING) {
      log_handler_->warn("Module destroyed while still running, forcing stop");
      forceStop();
    }
  }

  virtual bool init() = 0;
  virtual bool start() = 0;
  virtual bool stop() = 0;
  virtual void join() = 0;

  // Enhanced lifecycle management
  ModuleState getState() const { return state_.load(); }
  bool isRunning() const { return state_.load() == ModuleState::RUNNING; }
  bool isStopped() const {
    auto s = state_.load();
    return s == ModuleState::STOPPED || s == ModuleState::UNINITIALIZED;
  }
  bool isInErrorState() const { return in_error_state_.load(); }

  // Module identification
  const std::string& getModuleName() const { return module_name_; }

  // Graceful shutdown with timeout
  bool gracefulStop(std::chrono::milliseconds timeout = std::chrono::milliseconds(30000));
  void forceStop();

  // Wait for state change
  bool waitForState(ModuleState target_state, std::chrono::milliseconds timeout = std::chrono::milliseconds(10000));

  // Dependency management
  void addDependency(std::shared_ptr<BaseModule> dependency);
  bool waitForDependencies(std::chrono::milliseconds timeout = std::chrono::milliseconds(30000));

protected:
  // Enhanced error state management
  void probe_error() {
    if (in_error_state_.load()) return;

    error_count_++;
    if (error_count_ >= error_threshold_) {
      in_error_state_ = true;
      setState(ModuleState::ERROR);
      log_handler_->warn("Module entered error state after {} consecutive errors", error_threshold_);

      // Notify waiting threads
      state_cv_.notify_all();
    }
  }

  // Reset error state when normal operation resumes
  void reset_error_state() {
    if (error_count_ > 0) {
      error_count_ = 0;
      if (in_error_state_.exchange(false)) {
        log_handler_->info("Module recovered from error state");

        // If we were in error state, transition back to running if appropriate
        if (state_.load() == ModuleState::ERROR) {
          setState(ModuleState::RUNNING);
        }
      }
    }
  }

  // State management helpers
  void setState(ModuleState new_state) {
    ModuleState old_state = state_.exchange(new_state);
    if (old_state != new_state) {
      log_handler_->debug("Module state changed: {} -> {}",
                         static_cast<int>(old_state), static_cast<int>(new_state));
      state_cv_.notify_all();
    }
  }

  // Check for shutdown request
  bool isShutdownRequested() const { return shutdown_requested_.load(); }

  // Request graceful shutdown
  void requestShutdown() {
    shutdown_requested_ = true;
    shutdown_cv_.notify_all();
  }

private:
  // Module identification
  std::string module_name_;

  // Error management
  std::atomic<uint32_t> error_count_;
  std::atomic<bool> in_error_state_;
  const uint32_t error_threshold_;

  // State management
  std::atomic<ModuleState> state_;
  mutable std::mutex state_mutex_;
  mutable std::condition_variable state_cv_;

  // Shutdown management
  std::atomic<bool> shutdown_requested_;
  std::condition_variable shutdown_cv_;
  std::chrono::milliseconds graceful_shutdown_timeout_;

  // Dependencies
  std::vector<std::weak_ptr<BaseModule>> dependencies_;
  mutable std::mutex dependencies_mutex_;
};
} // namespace aby_box

#endif // COMMON_BASE__BASE_MODULE_HPP_