// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef COMMON_BASE__BASE_MODULE_HPP_
#define COMMON_BASE__BASE_MODULE_HPP_

#include "common/loggable.hpp"
#include "common/macro.hpp"
#include "uorb/abs_time.h"
#include "uorb/publication.h"
#include "uorb/publication_multi.h"
#include "uorb/subscription.h"
#include "uorb/subscription_interval.h"
#include "utils/config_manager.hpp"
#include "utils/logging.hpp"
#include <functional>
#include <map>
#include <memory>
#include <string>

namespace aby_box {
class BaseModule : public Loggable {
public:
  BaseModule(const std::string &module_name) 
    : Loggable(module_name)
    , error_count_(0)
    , in_error_state_(false)
    , error_threshold_(10) {}
  virtual ~BaseModule() {}
  virtual bool init() = 0;
  virtual bool start() = 0;
  virtual bool stop() = 0;
  virtual void join() = 0;

protected:
  // Error state probe function to be called when error occurs
  void probe_error() {
    if (in_error_state_) return;
    
    error_count_++;
    if (error_count_ >= error_threshold_) {
      in_error_state_ = true;
      log_handler_->warn("Module entered error state after {} consecutive errors", error_threshold_);
    }
  }

  // Reset error state when normal operation resumes
  void reset_error_state() {
    if (error_count_ > 0) {
      error_count_ = 0;
      if (in_error_state_) {
        in_error_state_ = false;
        log_handler_->info("Module recovered from error state");
      }
    }
  }

  // Check if module is in error state
  bool is_in_error_state() const {
    return in_error_state_;
  }

private:
  uint32_t error_count_;
  bool in_error_state_;
  const uint32_t error_threshold_;
};
} // namespace aby_box

#endif // COMMON_BASE__BASE_MODULE_HPP_