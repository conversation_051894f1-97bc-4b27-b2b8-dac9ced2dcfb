#pragma once

#include <inttypes.h>
#include <uorb/uorb.h>

#ifdef __cplusplus
struct __EXPORT sensor_weight_s {
#else
struct sensor_weight_s {
#endif
  uint64_t timestamp;
  uint32_t device_id;
  float weight;      // 重量值
  float raw_value;   // 原始值
  float temperature; // 温度值
  uint8_t _padding0[4];
};

/* register this as object request broker structure */
ORB_DECLARE(sensor_weight, sensor_weight_s);