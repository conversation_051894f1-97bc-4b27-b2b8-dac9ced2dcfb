#pragma once

#include <inttypes.h>
#include <uorb/uorb.h>

#ifdef __cplusplus
struct __EXPORT camera_object_detection_s {
#else
struct camera_object_detection_s {
#endif
  uint64_t timestamp;           // 时间戳
  uint32_t frame_id;            // 帧ID
  uint8_t object_type;          // 目标类型: 0-猫, 1-狗, 2-人
  uint8_t object_count;         // 检测到的目标数量
  uint8_t current_object_index; // 当前目标索引
  float confidence;             // 置信度
  float bbox_x1;                // 边界框左上角x坐标
  float bbox_y1;                // 边界框左上角y坐标
  float bbox_x2;                // 边界框右下角x坐标
  float bbox_y2;                // 边界框右下角y坐标
  uint8_t _padding0[2];         // 对齐
};

// 定义目标类型枚举
enum object_type {
  OBJECT_CAT = 0,
  OBJECT_DOG = 1,
  OBJECT_PERSON = 2,
  OBJECT_UNKNOWN = 99,
};

/* register this as object request broker structure */
ORB_DECLARE(camera_object_detection, camera_object_detection_s);