#pragma once

#include <inttypes.h>
#include <uorb/uorb.h>

#ifdef __cplusplus
struct __EXPORT weight_calculate_s {
#else
struct weight_calculate_s {
#endif
  int64_t timestamp_enter;        // 时间戳
  int64_t timestamp_leave;        // 时间戳
  uint32_t device_id;        // 设备ID
  float weight_litter;       // 猫砂重量
  float weight_shit;         // 猫屎重量
  float weight_cat;          // 猫的重量
  uint8_t _padding0[4];      // 对齐
};

/* register this as object request broker structure */
ORB_DECLARE(weight_calculate, weight_calculate_s);