#pragma once

#include <inttypes.h>
#include <uorb/uorb.h>

#ifdef __cplusplus
struct __EXPORT cat_event_s {
#else
struct cat_event_s {
#endif
  int64_t timestamp;   // 事件发生时间戳
  uint32_t device_id;   // 设备ID
  uint8_t event_type;   // 事件类型: 0-猫进入, 1-猫离开
  float weight;         // 事件发生时的重量
  float accel_x;        // 事件发生时的加速度x
  float accel_y;        // 事件发生时的加速度y
  float accel_z;        // 事件发生时的加速度z
  uint8_t _padding0[3]; // 对齐
};

enum cat_event_type {
  CAT_ENTER = 0,
  CAT_LEAVE = 1,
};

/* register this as object request broker structure */
ORB_DECLARE(cat_event, cat_event_s);