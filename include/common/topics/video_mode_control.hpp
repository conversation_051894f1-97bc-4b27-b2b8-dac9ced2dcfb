#pragma once

#include <inttypes.h>
#include <uorb/uorb.h>

#ifdef __cplusplus
struct __EXPORT video_mode_control_s {
#else
struct video_mode_control_s {
#endif
  uint64_t timestamp;     // 命令发送时间戳
  uint32_t device_id;     // 设备ID
  uint8_t video_mode;     // 视频模式: 0-彩色, 1-灰度
  uint8_t _padding0[3];   // 对齐
};

enum video_mode_type {
  VIDEO_MODE_COLOR = 0,   // 彩色模式
  VIDEO_MODE_GRAY = 1,    // 灰度模式
};

/* register this as object request broker structure */
ORB_DECLARE(video_mode_control, video_mode_control_s);
