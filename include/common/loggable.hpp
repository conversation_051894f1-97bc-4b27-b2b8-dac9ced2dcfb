// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef COMMON_BASE__LOGGABLE_HPP_
#define COMMON_BASE__LOGGABLE_HPP_

#include "utils/config_manager.hpp"
#include "utils/logging.hpp"
#include <functional>
#include <map>
#include <memory>
#include <string>

namespace aby_box {
class Loggable {
public:
  Loggable(const std::string &module_name)
      : log_handler_(std::make_shared<LogHandler>(module_name)) {}

  virtual ~Loggable() {}

protected:
  std::shared_ptr<LogHandler> log_handler_;
};
} // namespace aby_box

#endif // COMMON_BASE__LOGGABLE_HPP_