// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
#ifndef MODULE__WIFI_HPP_
#define MODULE__WIFI_HPP_

#include "common/base_module.hpp"
#include "utils/api_client.h"

#include <atomic>
#include <chrono>
#include <cstdio>
#include <cstdlib>
#include <fstream>
#include <iostream>
#include <memory>
#include <string>
#include <thread>
#include <mutex>
#include <condition_variable>

namespace aby_box {

class Wifi : public BaseModule {
public:
  // 单例模式接口
  static Wifi& getInstance() {
    static Wifi instance("WiFi");
    return instance;
  }

  // 删除拷贝构造函数和赋值操作符
  Wifi(const Wifi&) = delete;
  Wifi& operator=(const Wifi&) = delete;

  bool start() override;
  bool init() override;
  bool stop() override;
  void join() override;
  
  // 公共WiFi连接接口
  bool connectWifi(const std::string &ssid, const std::string &password);
  bool disconnectWifi();
  bool reconnectWifi();
  bool isConnected();

  // 网络状态查询接口
  bool isNetworkStable() const;
  int getSignalStrength() const;
  std::string getConnectionStatus() const;

private:
  explicit Wifi(const std::string &module_name)
      : BaseModule(module_name), is_running_(false) {}
  ~Wifi() {}

  void network_check_loop();
  bool check_network_connectivity();
  void stop_wpa_supplicant();
  bool connect(const std::string &ssid, const std::string &password);
  bool connect_wpa_supplicant();

  // 增强的网络连接管理
  bool performReconnectWithBackoff();
  bool waitForNetworkStability(int timeout_seconds = 30);
  void resetConnectionState();
  bool validateNetworkConfiguration() const;
  void updateSignalStrength();

private:
  std::unique_ptr<std::thread> network_check_thread_;
  std::atomic<bool> is_running_;
  mutable std::mutex wifi_mutex_;  // 保护WiFi操作的互斥锁
  std::condition_variable network_cv_;  // 网络状态变化通知
  std::string current_ssid_;
  std::string current_password_;
  std::atomic<bool> last_connected_state_{false};

  // 增强的网络状态管理
  std::atomic<int> consecutive_failures_{0};
  std::atomic<int> signal_strength_{0};
  std::chrono::steady_clock::time_point last_successful_connection_;
  std::chrono::steady_clock::time_point last_reconnect_attempt_;
  std::atomic<bool> network_stable_{false};

  // 重连策略配置
  static constexpr int MAX_CONSECUTIVE_FAILURES = 5;
  static constexpr int RECONNECT_BASE_DELAY_MS = 1000;  // 1秒基础延迟
  static constexpr int RECONNECT_MAX_DELAY_MS = 30000;  // 30秒最大延迟
  static constexpr int NETWORK_CHECK_INTERVAL_MS = 30000;  // 30秒检查间隔
  static constexpr int STABILITY_CHECK_DURATION_MS = 10000;  // 10秒稳定性检查
};

} // namespace aby_box

#endif // MODULE__WIFI_HPP_