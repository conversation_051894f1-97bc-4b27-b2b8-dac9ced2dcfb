// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef MODULE__OTA_MANAGER_HPP_
#define MODULE__OTA_MANAGER_HPP_

#include "common/base_module.hpp"
#include "module/initialization_manager.hpp"
#include <atomic>
#include <mutex>
#include <thread>
#include <memory>
#include <map>
#include <string>
#include <functional>
#include <curl/curl.h>
#include <chrono>

namespace aby_box {

// 前向声明
class CloudSyncManager;

// 下载进度回调函数类型
using DownloadProgressCallback = std::function<void(size_t downloaded, size_t total, double percentage)>;

class OtaManager : public BaseModule {
public:
  explicit OtaManager(const std::string &module_name);
  ~OtaManager();

  bool init() override;
  bool start() override;
  bool stop() override;
  void join() override;

  // OTA specific methods
  bool startUpgrade(const std::string &target_version = "", const std::string &ota_url = "", const std::string &ota_md5_url = "");
  bool isUpgrading() const { return is_upgrading_.load(); }
  std::string getTargetVersion() const { return target_version_; }
  std::string getOtaUrl() const { return ota_url_; }
  std::string getOtaMd5Url() const { return ota_md5_url_; }
  
  // Register modules that need to be stopped during OTA
  void registerModule(const std::string &module_name, std::shared_ptr<BaseModule> module);
  
  // Set CloudSyncManager reference for failure notification
  void setCloudSyncManager(std::shared_ptr<CloudSyncManager> cloud_sync) { cloud_sync_manager_ = cloud_sync; }

  // File download functions
  bool downloadFileWithRetry(const std::string &url, const std::string &local_path, 
                            const std::string &description = "");

  // System-level OTA cooldown management
  bool isInSystemOtaCooldown() const;
  void recordSystemOtaFailureAndRestart(const std::string &failed_version);
  void clearSystemOtaCooldown();

private:
  // Helper functions
  bool restartSystem();
  void stopAllModules();
  void updateOtaStatus(const std::string &status);
  
  // Download helper functions
  static size_t writeCallback(void *contents, size_t size, size_t nmemb, void *userp);
  static size_t progressCallback(void *clientp, curl_off_t dltotal, curl_off_t dlnow, 
                                curl_off_t ultotal, curl_off_t ulnow);
  bool downloadFile(const std::string &url, const std::string &local_path, 
                   const std::string &description, 
                   DownloadProgressCallback progress_callback);
  void cleanupExpiredRetryRecords();
  
  // MD5 verification functions
  bool verifyFileMd5(const std::string &file_path, const std::string &md5_file_path);
  std::string calculateFileMd5(const std::string &file_path);
  
  // System-level cooldown management
  bool loadSystemCooldownState();
  void saveSystemCooldownState(const std::string &failed_version);
  void removeSystemCooldownState();
  
  // Member variables
  std::atomic<bool> is_running_{false};
  std::atomic<bool> is_upgrading_{false};
  std::string target_version_;
  std::string ota_url_;
  std::string ota_md5_url_;
  
  // Map of modules to manage during OTA
  std::map<std::string, std::shared_ptr<BaseModule>> managed_modules_;
  
  // Mutex for thread safety
  std::mutex modules_mutex_;
  
  // CloudSyncManager reference for failure notification
  std::shared_ptr<CloudSyncManager> cloud_sync_manager_;
  
  // Download progress tracking
  struct DownloadContext {
    std::ofstream* file;
    DownloadProgressCallback progress_callback;
    std::string description;
    size_t resume_from; // 保留但不使用，为了结构兼容性
    bool* cancel_flag;
  };
  
  // Download retry management
  struct DownloadRetryInfo {
    std::string url;
    std::chrono::system_clock::time_point last_failure_time;
    int consecutive_failures;
    bool in_cooldown;
  };
  
  mutable std::mutex download_retry_mutex_;
  std::map<std::string, DownloadRetryInfo> download_retry_map_;
  
  // System-level OTA cooldown (persistent across reboots)
  static constexpr const char* SYSTEM_COOLDOWN_FILE = "/etc/cfg/ota_cooldown_state";
  static constexpr std::chrono::hours SYSTEM_COOLDOWN_PERIOD{24}; // 24小时系统级冷却期
};

} // namespace aby_box

#endif // MODULE__OTA_MANAGER_HPP_