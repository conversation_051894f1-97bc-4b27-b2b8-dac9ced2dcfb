#ifndef MODULE__GPIO_MANAGER_HPP_
#define MODULE__GPIO_MANAGER_HPP_

#include "common/base_module.hpp"
#include <atomic>
#include <chrono>
#include <functional>
#include <memory>
#include <thread>
#include <unordered_map>
#include <vector>

namespace aby_box {

enum class GpioDirection {
    INPUT,
    OUTPUT
};

enum class GpioEdge {
    NONE,
    RISING,
    FALLING,
    BOTH
};

struct GpioConfig {
    int pin;
    GpioDirection direction;
    int initial_value = 0;  // 仅对输出有效
    int debounce_time_ms = 50;  // 防抖时间
    GpioEdge edge = GpioEdge::NONE;  // 边沿触发模式
    
    GpioConfig(int p, GpioDirection dir) 
        : pin(p), direction(dir) {}
    
    GpioConfig(int p, GpioDirection dir, int debounce_ms)
        : pin(p), direction(dir), debounce_time_ms(debounce_ms) {}
};

// GPIO事件回调函数类型
using GpioCallback = std::function<void(int pin, int value, uint64_t timestamp)>;

class GpioManager : public BaseModule {
public:
    explicit GpioManager(const std::string &module_name);
    ~GpioManager();

    bool init() override;
    bool start() override;
    bool stop() override;
    void join() override;

    // GPIO配置和管理
    bool configure_gpio(const GpioConfig& config);
    bool remove_gpio(int pin);
    
    // GPIO读写操作
    int read_gpio(int pin);
    bool write_gpio(int pin, int value);
    
    // 事件监听
    bool register_callback(int pin, GpioCallback callback);
    bool unregister_callback(int pin);
    
    // 批量操作
    bool configure_multiple_gpios(const std::vector<GpioConfig>& configs);
    std::vector<int> read_multiple_gpios(const std::vector<int>& pins);

private:
    struct GpioInfo {
        GpioConfig config;
        std::atomic<int> last_value;
        std::chrono::steady_clock::time_point last_trigger_time;
        GpioCallback callback;
        bool is_monitoring;
        
        GpioInfo(const GpioConfig& cfg) 
            : config(cfg), last_value(0), 
              last_trigger_time(std::chrono::steady_clock::now()),
              is_monitoring(false) {}
    };

    void monitor_loop();
    bool init_single_gpio(const GpioConfig& config);
    void cleanup_single_gpio(int pin);
    bool is_debounce_elapsed(GpioInfo& gpio_info);
    std::string get_gpio_path(int pin, const std::string& attribute = "");

    std::unique_ptr<std::thread> monitor_thread_;
    std::atomic<bool> is_running_{false};
    
    // GPIO信息存储
    std::unordered_map<int, std::unique_ptr<GpioInfo>> gpio_map_;
    std::mutex gpio_map_mutex_;
    
    // 监控相关
    static constexpr int MONITOR_INTERVAL_MS = 10;  // 监控间隔
};

} // namespace aby_box

#endif // MODULE__GPIO_MANAGER_HPP_ 