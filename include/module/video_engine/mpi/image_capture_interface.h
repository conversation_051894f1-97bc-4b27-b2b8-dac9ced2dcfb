#ifndef IMAGE_CAPTURE_INTERFACE_H_
#define IMAGE_CAPTURE_INTERFACE_H_

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

// 初始化图像捕获管理器
int init_image_capture_manager(void);

// 销毁图像捕获管理器
void destroy_image_capture_manager(void);

// 捕获图像帧 - 由video_engine调用
void capture_image_frame(const uint8_t* frame_data, uint32_t width, uint32_t height, 
                        float confidence, uint64_t timestamp);

#ifdef __cplusplus
}
#endif

#endif // IMAGE_CAPTURE_INTERFACE_H_ 