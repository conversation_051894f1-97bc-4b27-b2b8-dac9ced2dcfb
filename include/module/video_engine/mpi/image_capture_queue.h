#ifndef IMAGE_CAPTURE_QUEUE_H
#define IMAGE_CAPTURE_QUEUE_H

#include <queue>
#include <mutex>
#include <condition_variable>
#include <vector>
#include <cstdint>

// 图像捕获数据结构
typedef struct {
  uint8_t *pYData;
  uint8_t *pUVData;
  uint32_t width;
  uint32_t height;
  uint32_t yStride;
  uint32_t uvStride;
  uint32_t yLength;
  uint32_t uvLength;
  float score;
  uint64_t timestamp;
} ImageCaptureData;

// 图像捕获队列管理类
class ImageCaptureQueue {
public:
  static ImageCaptureQueue& getInstance();
  
  void pushFrame(const ImageCaptureData& data);
  bool popFrame(ImageCaptureData& data);
  void shutdown();
  bool isShutdown() const { return shutdown_; }
  
  // 置信度处理功能：检查是否应该处理该置信度
  bool shouldProcessConfidence(float score);
  
  // 清空置信度数据，为下一次任务做准备
  void clearConfidenceData();

private:
  ImageCaptureQueue() = default;
  ~ImageCaptureQueue() = default;
  ImageCaptureQueue(const ImageCaptureQueue&) = delete;
  ImageCaptureQueue& operator=(const ImageCaptureQueue&) = delete;

  std::queue<ImageCaptureData> queue_;
  std::mutex mutex_;
  std::condition_variable cv_;
  bool shutdown_ = false;
  static constexpr size_t MAX_QUEUE_SIZE = 50;
  
  // 置信度处理相关：维护最高的三个置信度
  std::vector<float> top_confidences_;
  std::mutex confidence_mutex_;
  static constexpr size_t MAX_TOP_CONFIDENCES = 3; // 保留最高的3个置信度值
  static constexpr float CONFIDENCE_EPSILON = 0.001f; // 置信度比较精度
};

#endif // IMAGE_CAPTURE_QUEUE_H 