#ifndef VIDEO_DETECTOR_ENGINE_H
#define VIDEO_DETECTOR_ENGINE_H

#include "common/base_module.hpp"

// 前向声明
namespace aby_box {
  class VideoEngine;
  enum class CameraErrorType;
}
#include "common/topics/camera_object_detection.hpp"
#include "common/topics/cat_event.hpp"
#include "cvi_tdl.h"
#include "module/video_engine/mpi/middleware_utils.h"
#include "module/video_engine/mpi/image_capture_queue.h"
#include "sample_utils.h"
#include "vi_vo_utils.h"
#include <core/utils/vpss_helper.h>
#include <cvi_comm.h>
#include <pthread.h>
#include <rtsp.h>
#include <sample_comm.h>
#include <signal.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <memory>
#include <atomic>

extern volatile bool bExit;
static cvtdl_object_t g_stObjMeta = {0};
static uint32_t g_size = 0;

// 全局变量声明
extern std::atomic<aby_box::VideoEngine*> g_video_engine_instance;

typedef struct {
  SAMPLE_TDL_MW_CONTEXT *pstMWContext;
  cvitdl_service_handle_t stServiceHandle;
  CVI_TDL_SUPPORTED_MODEL_E enOdModelId;
} SAMPLE_TDL_VENC_THREAD_ARG_S;

typedef struct {
  ODInferenceFunc inference_func;
  CVI_TDL_SUPPORTED_MODEL_E enOdModelId;
  cvitdl_handle_t stTDLHandle;
} SAMPLE_TDL_TDL_THREAD_ARG_S;



void *run_venc(void *args);
void *run_tdl_thread(void *args);
void *run_image_capture_thread(void *args);
void *run_cat_event_listener_thread(void *args);
void *run_audio(void *args);
static void SampleHandleSig(CVI_S32 signo);
int launch_main();

// 猫帧数统计相关函数
uint32_t get_cat_frame_count();
void reset_cat_frame_count();

#endif // VIDEO_DETECTOR_ENGINE_H
