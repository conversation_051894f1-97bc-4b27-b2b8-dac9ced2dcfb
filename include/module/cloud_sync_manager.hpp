// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef MODULE__CLOUD_SYNC_MANAGER_HPP_
#define MODULE__CLOUD_SYNC_MANAGER_HPP_

#include "common/base_module.hpp"
#include "common/topics/cat_event.hpp"
#include "common/topics/sensor_accel.hpp"
#include "common/topics/sensor_weight.hpp"
#include "common/topics/weight_base.hpp"
#include "common/topics/weight_calculate.hpp"
#include "utils/api_client.h"
#include "module/ota/ota_manager.hpp"
#include "module/initialization_manager.hpp"
#include <atomic>
#include <mutex>
#include <thread>
#include <fstream>    // 添加文件流操作支持
#include <filesystem> // 添加文件系统操作支持
#include <ctime>      // 添加时间操作支持
#include <iomanip>    // 添加格式化输出支持

namespace aby_box {

class CloudSyncManager : public BaseModule {
public:
  explicit CloudSyncManager(const std::string &module_name);
  ~CloudSyncManager();

  bool init() override;
  bool start() override;
  bool stop() override;
  void join() override;
  
  // Set OTA manager reference for triggering updates
  void setOtaManager(std::shared_ptr<OtaManager> ota_manager) { ota_manager_ = ota_manager; }

  // OTA失败通知接口，供OtaManager调用
  void notifyOtaFailure(const std::string &failed_version);
  
  // 清除OTA失败记录，当服务器提供新版本时调用
  void clearOtaFailureRecord();
  
  // OTA已处理版本管理接口
  void markOtaVersionAsProcessed(const std::string &version);
  bool isOtaVersionAlreadyProcessed(const std::string &version) const;
  void clearProcessedOtaVersion();

private:
  /**
   * 重量数据监听线程函数
   * 负责监听weight_calculate消息并处理收到的数据
   * 当收到有效的重量数据时，通过API上传视频记录
   */
  void weight_data_listener();
  
  /**
   * 心跳包发送线程函数
   * 每2分钟发送一次心跳包，并检查是否需要OTA升级
   */
  void heartbeat_sender();
  
  /**
   * 检查心跳包返回数据，判断是否需要OTA升级
   * @param response 心跳包返回的JSON数据
   * @return 是否触发了OTA升级
   */
  bool checkOtaUpgrade(const nlohmann::json &response);
  
  /**
   * 检查当前时间是否在OTA更新时间窗口内
   * @param start_hour 更新开始时间（24小时制，0-23）
   * @param end_hour 更新结束时间（24小时制，0-23）
   * @return 如果当前时间在更新窗口内返回true，否则返回false
   *         如果start_hour >= end_hour，表示立即更新，返回true
   */
  bool isCurrentTimeInUpdateWindow(int start_hour, int end_hour) const;
  
  // 记录上一次处理的消息时间戳以避免重复处理
  uint64_t last_processed_timestamp_{0};
  std::mutex last_timestamp_mutex_;
  
  std::atomic<bool> is_running_{false};
  std::unique_ptr<std::thread> listener_thread_;
  std::unique_ptr<std::thread> heartbeat_thread_;
  
  // OTA manager reference
  std::shared_ptr<OtaManager> ota_manager_;
  
  // OTA失败版本记录机制
  std::string last_failed_ota_version_;  // 记录上次失败的OTA版本
  std::mutex ota_failure_mutex_;         // 保护失败版本记录的互斥锁
  
  // OTA已处理版本记录机制
  std::string last_processed_ota_version_;  // 记录上次已处理的OTA版本
  mutable std::mutex ota_processed_mutex_;  // 保护已处理版本记录的互斥锁
  
  // 录制文件路径配置
  const std::string recordings_path_{"/mnt/recordings/"}; // 录像存放的基础路径
  const std::string json_filename_{"video_data.json"};    // JSON数据文件名
  
  // 心跳包发送间隔 (5 minutes in milliseconds)
  const unsigned int heartbeat_interval_ms_{15 * 60 * 1000};
};

} // namespace aby_box

#endif // MODULE__CLOUD_SYNC_MANAGER_HPP_
