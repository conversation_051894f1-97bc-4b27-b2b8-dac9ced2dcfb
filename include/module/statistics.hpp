// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef MODULE__STATISTICS_HPP_
#define MODULE__STATISTICS_HPP_

#include <atomic>
#include <chrono>
#include <ctime>
#include <string>

#include "common/base_module.hpp"
#include "utils/api_client.h"
#include "module/initialization_manager.hpp"

namespace aby_box {

class Statistics : public BaseModule {
public:
  explicit Statistics(const std::string &module_name);
  ~Statistics() override;

  bool init() override;
  bool start() override;
  bool stop() override;
  void join() override;

  // 获取当前时区
  std::string get_timezone() const;
  int64_t get_boot_timestamp() const;
  uint64_t get_uptime() const;

private:
  void stats_loop();
  std::string get_system_timezone();
  
  bool network_check();
  bool verify_timezone();
  std::string get_ip_timezone();
  void update_timezone(const std::string& new_tz);
  
  std::atomic<bool> network_ready_{false};
  std::atomic<bool> timezone_verified_{false};
  static constexpr int FAST_CHECK_INTERVAL_MS = 1000;   // 1hz
  static constexpr int SLOW_CHECK_INTERVAL_MS = 100000; // 0.01hz

  std::unique_ptr<std::thread> stats_thread_;
  std::atomic<bool> is_running_{false};
  
  std::string timezone_;
  std::chrono::system_clock::time_point boot_time_;
  std::atomic<uint64_t> uptime_seconds_{0};
};
  
}  // namespace aby_box

#endif  // MODULE__STATISTICS_HPP_
