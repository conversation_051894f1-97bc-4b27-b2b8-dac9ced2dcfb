// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef MODULE__BOX_DETECTOR_HPP_
#define MODULE__BOX_DETECTOR_HPP_

#include "common/base_module.hpp"
#include "common/topics/camera_object_detection.hpp"
#include "common/topics/cat_event.hpp"
#include "common/topics/sensor_accel.hpp"
#include "common/topics/sensor_weight.hpp"
#include "common/topics/weight_base.hpp"
#include "utils/api_client.h"
#include <array>
#include <atomic>
#include <chrono>
#include <curl/curl.h>
#include <deque>
#include <memory>
#include <string>
#include <thread>

namespace aby_box {

struct DetectionConfig {
  float weight_threshold = 1000.0f;    // g, 体重变化阈值
  float accel_threshold = 2.0f;       // m/s², 加速度阈值
  float cusum_threshold = 300.0f;     // CUSUM算法阈值
  float drift = 0.1f;                 // CUSUM漂移参数
  float weight_limitation = 30000.0f; // g, 体重上限
  float base_weight = 11500.0f;       // g, 猫砂基础重量
  float weight_tolerance = 1000.0f;   // g, 基础重量允许的波动范围
};

class BoxDetector : public BaseModule {
public:
  explicit BoxDetector(const std::string &module_name);
  ~BoxDetector();

  bool init() override;
  bool start() override;
  bool stop() override;
  void join() override;
  void set_detection_config(const DetectionConfig &config) { config_ = config; }

private:
  void detection_loop();
  void sensor_subscriber();

  // CUSUM算法相关
  bool detect_weight_change(float weight);
  void reset_cusum();

  // 事件检测与发布
  void check_cat_event();
  void publish_cat_event(uint8_t event_type, int64_t timestamp);

  // 高斯滤波器相关
  static constexpr size_t GAUSSIAN_WINDOW = 5;  // 滤波窗口大小
  static constexpr float GAUSSIAN_SIGMA = 1.0f; // 高斯分布的标准差
  std::array<float, GAUSSIAN_WINDOW> weight_buffer_{};
  std::array<float, GAUSSIAN_WINDOW> gaussian_kernel_{};
  size_t buffer_index_{0};

  // 高斯滤波器方法
  void init_gaussian_kernel();
  float apply_gaussian_filter(float new_weight);

  std::unique_ptr<std::thread> detection_thread_;
  std::unique_ptr<std::thread> sensor_thread_;
  std::atomic<bool> is_running_{false};

  // 传感器数据 - 使用原子变量
  std::atomic<float> latest_weight_{0.0f};
  std::atomic<float> latest_accel_x_{0.0f};
  std::atomic<float> latest_accel_y_{0.0f};
  std::atomic<float> latest_accel_z_{0.0f};

  // CUSUM算法变量
  std::atomic<float> cusum_pos_{0.0f};
  std::atomic<float> cusum_neg_{0.0f};
  std::atomic<float> prev_weight_{0.0f};

  // 事件触发控制
  std::atomic<uint64_t> last_event_time_{0}; // 上次事件触发时间
  std::atomic<float> last_accel_y_{0.0f};    // 上次Y轴加速度值
  static constexpr uint64_t EVENT_COOLDOWN_US = 1000000; // 保持1秒冷却时间

  // 猫的状态跟踪
  std::atomic<bool> cat_in_box_{false};   // 猫是否在盆内
  std::atomic<bool> weight_stable_{true}; // 重量是否稳定
  static constexpr uint64_t WEIGHT_STABLE_TIME_US =
      200000; // 减少稳定时间到200ms
  std::atomic<uint64_t> last_weight_change_time_{0}; // 上次重量变化时间

  DetectionConfig config_;
  uorb::PublicationData<uorb::msg::cat_event> pub_cat_event_;

  std::string WEIGHT_API_URL;
  std::string AUTH_TOKEN;
  CURL *curl_{nullptr};
  struct curl_slist *headers_{nullptr}; // 添加headers成员变量

  // HTTP请求方法
  void send_weight_data(float weight);
  static size_t write_callback(char *ptr, size_t size, size_t nmemb,
                               void *userdata) {
    return size * nmemb; // 不处理响应数据
  }

  bool is_weight_above_base(float weight) const {
    return weight > (config_.base_weight + config_.weight_threshold);
  }
  bool is_weight_below_base(float weight) const {
    return weight < (config_.base_weight - config_.weight_tolerance);
  }
  bool is_weight_within_base(float weight) const {
    return weight >= (config_.base_weight - config_.weight_tolerance) &&
           weight <= (config_.base_weight + config_.weight_tolerance);
  }
  

  bool verify_cat_presence(uint64_t event_time);

  static constexpr uint64_t IMAGE_VERIFY_TIMEOUT_US =
      5000000; // 5秒图像验证超时
  static constexpr uint64_t CAT_PRESENCE_CHECK_US = 500000; // 500ms检查间隔

  std::atomic<bool> cat_detected_by_camera_{false};
  std::atomic<uint64_t> last_cat_detection_time_{0};
};

} // namespace aby_box

#endif // MODULE__BOX_DETECTOR_HPP_