// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef MODULE__INITIALIZATION_MANAGER_HPP_
#define MODULE__INITIALIZATION_MANAGER_HPP_

#include "common/base_module.hpp"
#include "utils/config_manager.hpp"
#include "utils/logging.hpp"
#include <curl/curl.h>
#include <nlohmann/json.hpp>
#include <string>
#include <thread>
#include <atomic>
#include <memory>
#include "utils/api_client.h"

namespace aby_box {

class InitializationManager : public BaseModule {
public:
  static InitializationManager &getInstance();

  // Delete copy constructor and assignment operator
  InitializationManager(const InitializationManager &) = delete;
  InitializationManager &operator=(const InitializationManager &) = delete;

  bool init() override;
  bool start() override;
  bool stop() override;
  void join() override;

  // 获取初始化状态
  bool isInitialized() const { return api_init_complete_; }
  
  // 获取设备信息（实时从配置文件读取最新值）
  std::string getDeviceId() const { return device_id_; }
  std::string getUserId() const { return user_id_; }
  std::string getHardwareSn() const { return hardware_sn_; }

private:
  InitializationManager(); // Private constructor
  ~InitializationManager() = default;

  // 初始化线程主函数，负责设备注册并持续监控配置文件变化
  void initialization_loop();
  
  // 设备注册和绑定相关方法
  bool register_device();
  bool fetch_user_id_by_hardware_sn(); // 通过硬件序列号获取 user_id
  
  // 配置和网络请求相关方法
  void loadConfigValues(); // 从配置文件重新加载最新值，并记录变化
  static size_t WriteCallback(void *contents, size_t size, size_t nmemb, std::string *userp);
  CURLcode performSimpleGetRequest(const std::string &url, std::string &response);
  CURLcode performCurlRequest(const std::string &url, const std::string &method,
                              const std::string &postData, std::string &response, 
                              long *http_code = nullptr);

  // 成员变量
  std::unique_ptr<std::thread> initialization_thread_;
  std::atomic<bool> is_running_{false};
  std::atomic<bool> api_init_complete_{false};
  
  // 设备信息
  std::string device_id_;
  std::string user_id_;
  std::string hardware_sn_;
  std::string base_url_;
  
  // 日志处理器
  std::shared_ptr<LogHandler> log_handler_;
};

} // namespace aby_box

#endif // MODULE__INITIALIZATION_MANAGER_HPP_ 