#ifndef MODULE__WEIGHT_ANALYZER_HPP_
#define MODULE__WEIGHT_ANALYZER_HPP_

#include "common/base_module.hpp"
#include "common/topics/sensor_weight.hpp"
#include "common/topics/cat_event.hpp"
#include "common/topics/weight_base.hpp"
#include "common/topics/weight_calculate.hpp"
#include <errno.h>
#include <string.h>
#include <atomic>
#include <deque>
#include <chrono>
#include <memory>
#include <thread>
#include <algorithm>

#include <json-c/json.h>
#include <filesystem>

namespace aby_box
{

  class WeightAnalyzer : public BaseModule
  {
  public:
    explicit WeightAnalyzer(const std::string &module_name);
    ~WeightAnalyzer();

    bool init() override;
    bool start() override;
    bool stop() override;
    void join() override;

  private:
    const std::string save_dir = "/mnt/recordings";
    void analysis_loop();

    std::unique_ptr<std::thread> analysis_thread_;
    std::atomic<bool> is_running_{false};

    std::atomic<float> stable_weight_{0.0f};  // 存储稳定的重量值
    float cat_weight_median = 0.0f;           // 猫在盆内的重量中位数
    float last_stable_weight_before_cat = 0.0f; // 猫进入前最后一次稳定的重量(猫砂+已有排泄物)
    float first_stable_weight_after_cat_10 = 0.0f;
    float first_stable_weight_after_cat_50 = 0.0f; // 猫离开后第一次稳定的重量
    bool weight_after_cat_stabilized = false;      // 猫离开后重量是否已稳定
  };

} // namespace aby_box

#endif // MODULE__WEIGHT_ANALYZER_HPP_