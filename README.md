# ABY Box - 智能猫砂盆监控系统

## 项目简介

ABY Box 是一套基于CV181X芯片的智能猫砂盆监控系统，集成视觉识别、称重传感、运动检测等多种技术，实现对猫咪使用猫砂盆行为的全面监控和数据分析。

## 核心功能

### 🎯 智能检测
- **猫咪识别**: 基于YOLO模型的实时目标检测，准确识别猫咪进出猫砂盆
- **重量监测**: 高精度HX711称重传感器，实时监测重量变化
- **运动感知**: 三轴加速度传感器检测振动和运动状态
- **多传感器融合**: CUSUM算法结合多传感器数据提高检测准确性

### 📹 视频录制
- **自动录制**: 猫咪进入时自动开始录制，离开时停止
- **RTSP流**: 支持实时视频流推送
- **智能截图**: 基于置信度的最优图像捕获和存储
- **H264/H265编码**: 高效视频压缩和存储

### 📊 数据分析
- **重量计算**: 精确计算猫砂重量、猫咪体重、排泄物重量
- **行为统计**: 记录使用频次、时长等行为数据
- **健康监控**: 通过重量和使用模式分析健康状况

### ☁️ 云端同步
- **自动上传**: 录像和数据自动同步至云端存储
- **OTA更新**: 支持远程固件更新
- **API集成**: RESTful API支持第三方应用集成

## 系统架构

### 硬件平台
- **主控芯片**: CV181X (ARM Cortex-A53)
- **AI加速**: 集成TPU用于深度学习推理
- **传感器**: HX711称重传感器、三轴加速度计
- **摄像头**: 支持1080P实时视频捕获
- **网络**: WiFi连接，支持RTSP流

### 软件架构
```
├── 核心模块
│   ├── BoxDetector      # 猫咪检测和事件触发
│   ├── VideoEngine      # 视频录制和AI推理
│   ├── WeightAnalyzer   # 重量数据分析
│   └── ImageCapture     # 智能图像捕获
├── 传感器层
│   ├── Weighing         # 称重传感器接口
│   └── Lighting         # 补光控制
├── 网络通信
│   ├── WiFi             # 网络连接管理
│   ├── CloudSync        # 云端数据同步
│   └── APIClient        # API通信接口
└── 系统服务
    ├── OTA Manager      # 固件更新管理
    ├── Statistics       # 统计数据收集
    └── GPIO Manager     # 硬件接口控制
```

## 技术特性

### 🧠 AI算法
- **目标检测**: YOLO v8猫咪检测模型
- **置信度筛选**: 动态维护最高置信度检测结果
- **重复检测过滤**: 避免冗余数据处理
- **实时推理**: 优化的推理管道，低延迟检测

### 📈 数据处理
- **CUSUM算法**: 用于重量变化检测和事件触发
- **高斯滤波**: 传感器数据噪声抑制
- **滑动窗口**: 实时数据平滑和稳定性判断
- **多线程处理**: 并发数据采集和处理

### 🔧 系统优化
- **内存管理**: 智能队列管理避免内存泄露
- **CPU优化**: 多级缓存和智能跳帧处理
- **存储管理**: 自动清理和压缩存储
- **网络优化**: 断线重连和数据缓存

## 编译和部署

### 环境要求
- CMake 3.10+
- GCC支持C++17
- CV181X交叉编译工具链
- 相关依赖库 (OpenCV, FFmpeg, CURL等)

## 配置说明

### 检测参数
- **重量阈值**: 1000g (可调节敏感度)
- **置信度要求**: 60% (视觉检测最低置信度)
- **稳定时间**: 200ms (重量稳定判定时间)
- **事件冷却**: 1秒 (避免重复触发)

### 网络配置
- **RTSP端口**: 8554
- **API端点**: `/api/records`
- **心跳间隔**: 10秒
- **重试次数**: 3次

## 许可证

```
Copyright (c) 2024 animsi Technology Co., Ltd.
Licensed under the Apache License, Version 2.0
```

## 支持和反馈

本项目专为智能宠物监控应用设计，提供完整的硬件适配和软件解决方案。如需技术支持或功能定制，请联系开发团队。
