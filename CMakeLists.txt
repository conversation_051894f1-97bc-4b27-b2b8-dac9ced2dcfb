cmake_minimum_required(VERSION 3.10)

set(ABY_BOX_NAME "aby_box")
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED True)

project(${ABY_BOX_NAME})

# 检测架构并设置相应的编译选项
if(CMAKE_SYSTEM_PROCESSOR MATCHES "^(aarch64|arm64)$")
    # ARM64特定的编译选项
    set(ARCH_FLAGS "-march=armv8-a -mcpu=cortex-a53")
    message(STATUS "Building for ARM64 architecture")
elseif(CMAKE_SYSTEM_PROCESSOR MATCHES "^(x86_64|AMD64)$")
    # x86_64特定的编译选项
    set(ARCH_FLAGS "-march=native")
    message(STATUS "Building for x86_64 architecture")
else()
    # 通用选项
    set(ARCH_FLAGS "")
    message(STATUS "Building for generic architecture: ${CMAKE_SYSTEM_PROCESSOR}")
endif()

# 通用编译选项
set(COMMON_FLAGS "${ARCH_FLAGS} -Wno-psabi -DSPDLOG_FMT_EXTERNAL -D__CV181X__ -fpermissive")

# Debug 构建类型的编译选项
set(DEBUG_FLAGS "${COMMON_FLAGS} -g3 -O0 -fno-omit-frame-pointer -Wall -Wextra -DDEBUG -fstack-protector-all")

# Release 构建类型的编译选项
set(RELEASE_FLAGS "${COMMON_FLAGS} -O3 -DNDEBUG -ffast-math -fomit-frame-pointer -flto -ffunction-sections -fdata-sections -ftree-vectorize -fno-stack-protector")

# Release 链接器选项
set(RELEASE_LINKER_FLAGS "-Wl,--gc-sections -flto")

message(STATUS "CXX Flags: ${CMAKE_CXX_FLAGS}")
message(STATUS "CXX Flags Debug: ${CMAKE_CXX_FLAGS_DEBUG}")
message(STATUS "CXX Flags Release: ${CMAKE_CXX_FLAGS_RELEASE}")

set(CFG_DIR ${CMAKE_SOURCE_DIR}/cfg)
set(TOOLS_DIR ${CMAKE_SOURCE_DIR}/tools)
set(ETC_DIR "/etc")

find_package(CURL REQUIRED)

set(KERNEL_INC ${KERNEL_HEADERS}/include)

# Common libraries
set(COMMON_LIBRARIES
    curl 
    z  
    crypto
    ssl
    avcodec
    avformat
    avutil
    swscale
    swresample
    openjp2
    bz2
    drm
    spdlog
    fmt
    uorb
    zbar
    json-c
    dbus-1
)

# Libraries with potential circular dependencies
set(CIRCULAR_LIBRARIES
    af
    ae 
    awb 
    aaccomm2 
    aacdec2
    aacenc2
    aacsbrdec2
    aacsbrenc2
    isp_algo
    isp 
    cvi_bin
    cvi_audio 
    cvi_bin_isp
    cvi_dnvqe
    cvi_ispd2 
    cvi_ive 
    cvi_RES1
    cvi_ssp 
    cvi_ssp2 
    cvi_VoiceEngine
    cvi_vqe  
    ini 
    mipi_tx 
    misc 
    raw_dump 
    raw_replay
    rgn 
    sns_gc2083 
    sns_full
    tinyalsa 
    vdec 
    vo 
    vpss
    sys 
    venc 
    gdc
    vi 
    sample
    cvi_rtsp
    cvi_tdl
    cvikernel
    cvimath
    cviruntime
    opencv_core
    opencv_imgproc
    opencv_imgcodecs
)

# 创建 Release 版本目标
file(GLOB_RECURSE SRC_FILES "src/*.cc")
add_executable(${PROJECT_NAME} ${SRC_FILES})
target_include_directories(${PROJECT_NAME} PRIVATE "include" ${KERNEL_INC})
set_target_properties(${PROJECT_NAME} PROPERTIES 
    COMPILE_FLAGS "${RELEASE_FLAGS}"
    LINK_FLAGS "${RELEASE_LINKER_FLAGS}")

# Debug版本编译控制选项
option(BUILD_DEBUG "Build debug version" OFF)

# 创建 Debug 版本目标（仅当启用时）
if(BUILD_DEBUG)
    message(STATUS "Building debug version enabled")
    add_executable(${PROJECT_NAME}_debug ${SRC_FILES})
    target_include_directories(${PROJECT_NAME}_debug PRIVATE "include" ${KERNEL_INC})
    set_target_properties(${PROJECT_NAME}_debug PROPERTIES 
        COMPILE_FLAGS "${DEBUG_FLAGS}"
        OUTPUT_NAME "${PROJECT_NAME}_debug")
    
    # Link libraries for Debug version
    target_link_libraries(${PROJECT_NAME}_debug
        ${COMMON_LIBRARIES}
        -Wl,--start-group
        ${CIRCULAR_LIBRARIES}
        -Wl,--end-group
        CURL::libcurl
    )
    
    # 设置debug版本的安装目标
    set(DEBUG_INSTALL_TARGETS ${PROJECT_NAME} ${PROJECT_NAME}_debug)
else()
    message(STATUS "Debug version build disabled (use -DBUILD_DEBUG=ON to enable)")
    set(DEBUG_INSTALL_TARGETS ${PROJECT_NAME})
endif()

# Link libraries for Release version
target_link_libraries(${PROJECT_NAME}
    ${COMMON_LIBRARIES}
    -Wl,--start-group
    ${CIRCULAR_LIBRARIES}
    -Wl,--end-group
    CURL::libcurl
)



# Install targets
install(TARGETS ${DEBUG_INSTALL_TARGETS}
        RUNTIME DESTINATION ${CMAKE_INSTALL_PREFIX}/bin)
install(DIRECTORY ${CFG_DIR} 
        DESTINATION ${ETC_DIR} 
        USE_SOURCE_PERMISSIONS)
install(DIRECTORY ${TOOLS_DIR} 
        DESTINATION ${ETC_DIR} 
        USE_SOURCE_PERMISSIONS)