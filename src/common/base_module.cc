// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "common/base_module.hpp"
#include <algorithm>

namespace aby_box {

bool BaseModule::gracefulStop(std::chrono::milliseconds timeout) {
  log_handler_->info("Initiating graceful stop with timeout: {}ms", timeout.count());
  
  // Request shutdown
  requestShutdown();
  
  // Call the module's stop method
  if (!stop()) {
    log_handler_->warn("Module stop() method returned false");
  }
  
  // Wait for the module to reach stopped state
  if (waitForState(ModuleState::STOPPED, timeout)) {
    log_handler_->info("Module stopped gracefully");
    return true;
  } else {
    log_handler_->warn("Module did not stop within timeout, forcing stop");
    forceStop();
    return false;
  }
}

void BaseModule::forceStop() {
  log_handler_->warn("Force stopping module");
  
  shutdown_requested_ = true;
  setState(ModuleState::STOPPING);
  
  // Call stop method
  try {
    stop();
  } catch (const std::exception& e) {
    log_handler_->error("Exception during force stop: {}", e.what());
  }
  
  // Force state to stopped
  setState(ModuleState::STOPPED);
  
  log_handler_->info("Module force stopped");
}

bool BaseModule::waitForState(ModuleState target_state, std::chrono::milliseconds timeout) {
  std::unique_lock<std::mutex> lock(state_mutex_);
  
  return state_cv_.wait_for(lock, timeout, [this, target_state]() {
    return state_.load() == target_state;
  });
}

void BaseModule::addDependency(std::shared_ptr<BaseModule> dependency) {
  if (!dependency) {
    log_handler_->warn("Attempted to add null dependency");
    return;
  }
  
  std::lock_guard<std::mutex> lock(dependencies_mutex_);
  dependencies_.push_back(dependency);
  
  log_handler_->debug("Added dependency: {}", dependency->module_name_);
}

bool BaseModule::waitForDependencies(std::chrono::milliseconds timeout) {
  std::lock_guard<std::mutex> lock(dependencies_mutex_);
  
  auto start_time = std::chrono::steady_clock::now();
  
  for (auto it = dependencies_.begin(); it != dependencies_.end();) {
    auto dependency = it->lock();
    if (!dependency) {
      // Dependency has been destroyed, remove it
      it = dependencies_.erase(it);
      continue;
    }
    
    auto remaining_time = timeout - std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::steady_clock::now() - start_time);
    
    if (remaining_time <= std::chrono::milliseconds(0)) {
      log_handler_->warn("Timeout waiting for dependencies");
      return false;
    }
    
    if (!dependency->waitForState(ModuleState::RUNNING, remaining_time)) {
      log_handler_->warn("Dependency {} did not reach running state in time", 
                        dependency->module_name_);
      return false;
    }
    
    ++it;
  }
  
  log_handler_->debug("All dependencies are ready");
  return true;
}

} // namespace aby_box
