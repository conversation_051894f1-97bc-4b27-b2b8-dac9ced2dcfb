#include "common/topics/camera_object_detection.hpp"
#include <uorb/uorb.h>

static constexpr char orb_camera_object_detection_fields[] =
    "uint64_t timestamp;"
    "uint32_t frame_id;"
    "uint8_t object_type;"
    "uint8_t object_count;"
    "uint8_t current_object_index;"
    "float confidence;"
    "float bbox_x1;"
    "float bbox_y1;"
    "float bbox_x2;"
    "float bbox_y2;"
    "uint8_t[2] _padding0;";

ORB_DEFINE(camera_object_detection, struct camera_object_detection_s, 32,
           orb_camera_object_detection_fields, 1);