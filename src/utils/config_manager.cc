#include "utils/config_manager.hpp"
#include "module/wifi.hpp"
#include "utils/safe_file_ops.hpp"
#include <filesystem>
#include <chrono>
#include <iomanip>
#include <sstream>

namespace aby_box {

// Enhanced configuration management methods
bool ConfigManager::validateConfig() const {
  std::lock_guard<std::mutex> lock(mutex_);

  try {
    // Check required fields
    if (!config_.contains("data_upload_api") || !config_["data_upload_api"].contains("base_url")) {
      log_handler_->error("Missing required field: data_upload_api.base_url");
      return false;
    }

    if (!config_.contains("hardware_sn") || config_["hardware_sn"].get<std::string>().empty()) {
      log_handler_->error("Missing or empty hardware_sn");
      return false;
    }

    // Validate WiFi config if present
    if (config_.contains("wifi") && config_["wifi"].contains("is_valid") &&
        config_["wifi"]["is_valid"].get<bool>()) {
      if (!config_["wifi"].contains("ssid") || config_["wifi"]["ssid"].get<std::string>().empty()) {
        log_handler_->error("WiFi marked as valid but SSID is missing or empty");
        return false;
      }
    }

    // Validate URLs
    std::string base_url = config_["data_upload_api"]["base_url"].get<std::string>();
    if (base_url.empty() || (base_url.find("http://") != 0 && base_url.find("https://") != 0)) {
      log_handler_->error("Invalid base URL format: {}", base_url);
      return false;
    }

    log_handler_->debug("Configuration validation passed");
    return true;

  } catch (const std::exception& e) {
    log_handler_->error("Configuration validation failed: {}", e.what());
    return false;
  }
}

bool ConfigManager::createBackup() const {
  auto& safe_ops = getSafeFileOps();

  // Generate backup filename with timestamp
  auto now = std::chrono::system_clock::now();
  auto time_t = std::chrono::system_clock::to_time_t(now);
  std::stringstream ss;
  ss << config_path_ << ".backup." << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S");
  std::string backup_path = ss.str();

  auto result = safe_ops.copyFile(config_path_, backup_path, true);
  if (result == FileOpResult::SUCCESS) {
    log_handler_->info("Configuration backup created: {}", backup_path);

    // Clean up old backups (keep only last 5)
    cleanupOldBackups();
    return true;
  } else {
    log_handler_->error("Failed to create configuration backup: {}",
                       SafeFileOps::getErrorString(result));
    return false;
  }
}

bool ConfigManager::restoreFromBackup(const std::string& backup_path) {
  auto& safe_ops = getSafeFileOps();

  if (!safe_ops.fileExists(backup_path)) {
    log_handler_->error("Backup file does not exist: {}", backup_path);
    return false;
  }

  // Validate backup before restoring
  nlohmann::json backup_config;
  auto backup_content = safe_ops.readFileContent(backup_path);
  if (!backup_content) {
    log_handler_->error("Failed to read backup file: {}", backup_path);
    return false;
  }

  try {
    backup_config = nlohmann::json::parse(*backup_content);
  } catch (const std::exception& e) {
    log_handler_->error("Invalid JSON in backup file {}: {}", backup_path, e.what());
    return false;
  }

  // Create backup of current config before restoring
  createBackup();

  // Restore configuration
  auto result = safe_ops.copyFile(backup_path, config_path_, true);
  if (result == FileOpResult::SUCCESS) {
    // Reload configuration
    std::lock_guard<std::mutex> lock(mutex_);
    config_ = backup_config;

    log_handler_->info("Configuration restored from backup: {}", backup_path);
    return true;
  } else {
    log_handler_->error("Failed to restore configuration from backup: {}",
                       SafeFileOps::getErrorString(result));
    return false;
  }
}

void ConfigManager::cleanupOldBackups() const {
  try {
    std::filesystem::path config_dir = std::filesystem::path(config_path_).parent_path();
    std::string config_filename = std::filesystem::path(config_path_).filename();
    std::string backup_prefix = config_filename + ".backup.";

    std::vector<std::filesystem::path> backup_files;

    for (const auto& entry : std::filesystem::directory_iterator(config_dir)) {
      if (entry.is_regular_file()) {
        std::string filename = entry.path().filename();
        if (filename.find(backup_prefix) == 0) {
          backup_files.push_back(entry.path());
        }
      }
    }

    // Sort by modification time (newest first)
    std::sort(backup_files.begin(), backup_files.end(),
              [](const std::filesystem::path& a, const std::filesystem::path& b) {
                return std::filesystem::last_write_time(a) > std::filesystem::last_write_time(b);
              });

    // Keep only the 5 most recent backups
    for (size_t i = 5; i < backup_files.size(); ++i) {
      std::filesystem::remove(backup_files[i]);
      log_handler_->debug("Removed old backup: {}", backup_files[i].string());
    }

  } catch (const std::exception& e) {
    log_handler_->warn("Failed to cleanup old backups: {}", e.what());
  }
}

bool ConfigManager::connectUsingWifiModule(const std::string &ssid, const std::string &password) {
  try {
    // 获取WiFi模块的单例实例
    Wifi& wifi_module = Wifi::getInstance();
    
    // 使用WiFi模块连接网络
    bool success = wifi_module.connectWifi(ssid, password);
    
    if (success) {
      log_handler_->info("Successfully connected to WiFi using WiFi module: SSID={}", ssid);
    } else {
      log_handler_->error("Failed to connect to WiFi using WiFi module: SSID={}", ssid);
    }
    
    return success;
  } catch (const std::exception &e) {
    log_handler_->error("Exception while connecting WiFi: {}", e.what());
    return false;
  }
}

} // namespace aby_box 