#include "utils/config_manager.hpp"
#include "module/wifi.hpp"

namespace aby_box {

bool ConfigManager::connectUsingWifiModule(const std::string &ssid, const std::string &password) {
  try {
    // 获取WiFi模块的单例实例
    Wifi& wifi_module = Wifi::getInstance();
    
    // 使用WiFi模块连接网络
    bool success = wifi_module.connectWifi(ssid, password);
    
    if (success) {
      log_handler_->info("Successfully connected to WiFi using WiFi module: SSID={}", ssid);
    } else {
      log_handler_->error("Failed to connect to WiFi using WiFi module: SSID={}", ssid);
    }
    
    return success;
  } catch (const std::exception &e) {
    log_handler_->error("Exception while connecting WiFi: {}", e.what());
    return false;
  }
}

} // namespace aby_box 