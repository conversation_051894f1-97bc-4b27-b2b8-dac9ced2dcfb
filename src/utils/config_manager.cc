#include "utils/config_manager.hpp"
#include "module/wifi.hpp"
#include "utils/safe_file_ops.hpp"
#include <filesystem>
#include <chrono>
#include <iomanip>
#include <sstream>

namespace aby_box {

// Enhanced configuration management methods
bool ConfigManager::validateConfig() const {
  std::lock_guard<std::mutex> lock(mutex_);

  try {
    // Check required fields
    if (!config_.contains("data_upload_api") || !config_["data_upload_api"].contains("base_url")) {
      log_handler_->error("Missing required field: data_upload_api.base_url");
      return false;
    }

    if (!config_.contains("hardware_sn") || config_["hardware_sn"].get<std::string>().empty()) {
      log_handler_->error("Missing or empty hardware_sn");
      return false;
    }

    // Validate WiFi config if present
    if (config_.contains("wifi") && config_["wifi"].contains("is_valid") &&
        config_["wifi"]["is_valid"].get<bool>()) {
      if (!config_["wifi"].contains("ssid") || config_["wifi"]["ssid"].get<std::string>().empty()) {
        log_handler_->error("WiFi marked as valid but SSID is missing or empty");
        return false;
      }
    }

    // Validate URLs
    std::string base_url = config_["data_upload_api"]["base_url"].get<std::string>();
    if (base_url.empty() || (base_url.find("http://") != 0 && base_url.find("https://") != 0)) {
      log_handler_->error("Invalid base URL format: {}", base_url);
      return false;
    }

    log_handler_->debug("Configuration validation passed");
    return true;

  } catch (const std::exception& e) {
    log_handler_->error("Configuration validation failed: {}", e.what());
    return false;
  }
}

bool ConfigManager::createBackup() const {
  auto& safe_ops = getSafeFileOps();

  // Generate backup filename with timestamp
  auto now = std::chrono::system_clock::now();
  auto time_t = std::chrono::system_clock::to_time_t(now);
  std::stringstream ss;
  ss << config_path_ << ".backup." << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S");
  std::string backup_path = ss.str();

  auto result = safe_ops.copyFile(config_path_, backup_path, true);
  if (result == FileOpResult::SUCCESS) {
    log_handler_->info("Configuration backup created: {}", backup_path);

    // Clean up old backups (keep only last 5)
    cleanupOldBackups();
    return true;
  } else {
    log_handler_->error("Failed to create configuration backup: {}",
                       SafeFileOps::getErrorString(result));
    return false;
  }
}

bool ConfigManager::restoreFromBackup(const std::string& backup_path) {
  auto& safe_ops = getSafeFileOps();

  if (!safe_ops.fileExists(backup_path)) {
    log_handler_->error("Backup file does not exist: {}", backup_path);
    return false;
  }

  // Validate backup before restoring
  nlohmann::json backup_config;
  auto backup_content = safe_ops.readFileContent(backup_path);
  if (!backup_content) {
    log_handler_->error("Failed to read backup file: {}", backup_path);
    return false;
  }

  try {
    backup_config = nlohmann::json::parse(*backup_content);
  } catch (const std::exception& e) {
    log_handler_->error("Invalid JSON in backup file {}: {}", backup_path, e.what());
    return false;
  }

  // Create backup of current config before restoring
  createBackup();

  // Restore configuration
  auto result = safe_ops.copyFile(backup_path, config_path_, true);
  if (result == FileOpResult::SUCCESS) {
    // Reload configuration
    std::lock_guard<std::mutex> lock(mutex_);
    config_ = backup_config;

    log_handler_->info("Configuration restored from backup: {}", backup_path);
    return true;
  } else {
    log_handler_->error("Failed to restore configuration from backup: {}",
                       SafeFileOps::getErrorString(result));
    return false;
  }
}

void ConfigManager::cleanupOldBackups() const {
  try {
    std::filesystem::path config_dir = std::filesystem::path(config_path_).parent_path();
    std::string config_filename = std::filesystem::path(config_path_).filename();
    std::string backup_prefix = config_filename + ".backup.";

    std::vector<std::filesystem::path> backup_files;

    for (const auto& entry : std::filesystem::directory_iterator(config_dir)) {
      if (entry.is_regular_file()) {
        std::string filename = entry.path().filename();
        if (filename.find(backup_prefix) == 0) {
          backup_files.push_back(entry.path());
        }
      }
    }

    // Sort by modification time (newest first)
    std::sort(backup_files.begin(), backup_files.end(),
              [](const std::filesystem::path& a, const std::filesystem::path& b) {
                return std::filesystem::last_write_time(a) > std::filesystem::last_write_time(b);
              });

    // Keep only the 5 most recent backups
    for (size_t i = 5; i < backup_files.size(); ++i) {
      std::filesystem::remove(backup_files[i]);
      log_handler_->debug("Removed old backup: {}", backup_files[i].string());
    }

  } catch (const std::exception& e) {
    log_handler_->warn("Failed to cleanup old backups: {}", e.what());
  }
}

bool ConfigManager::connectUsingWifiModule(const std::string &ssid, const std::string &password) {
  try {
    // 获取WiFi模块的单例实例
    Wifi& wifi_module = Wifi::getInstance();
    
    // 使用WiFi模块连接网络
    bool success = wifi_module.connectWifi(ssid, password);
    
    if (success) {
      log_handler_->info("Successfully connected to WiFi using WiFi module: SSID={}", ssid);
    } else {
      log_handler_->error("Failed to connect to WiFi using WiFi module: SSID={}", ssid);
    }
    
    return success;
  } catch (const std::exception &e) {
    log_handler_->error("Exception while connecting WiFi: {}", e.what());
    return false;
  }
}

// Additional enhanced configuration methods
std::vector<std::string> ConfigManager::listBackups() const {
  std::vector<std::string> backups;

  try {
    std::filesystem::path config_dir = std::filesystem::path(config_path_).parent_path();
    std::string config_filename = std::filesystem::path(config_path_).filename();
    std::string backup_prefix = config_filename + ".backup.";

    for (const auto& entry : std::filesystem::directory_iterator(config_dir)) {
      if (entry.is_regular_file()) {
        std::string filename = entry.path().filename();
        if (filename.find(backup_prefix) == 0) {
          backups.push_back(entry.path().string());
        }
      }
    }

    // Sort by modification time (newest first)
    std::sort(backups.begin(), backups.end(),
              [](const std::string& a, const std::string& b) {
                return std::filesystem::last_write_time(a) > std::filesystem::last_write_time(b);
              });

  } catch (const std::exception& e) {
    log_handler_->warn("Failed to list backups: {}", e.what());
  }

  return backups;
}

bool ConfigManager::verifyConfigIntegrity() const {
  auto& safe_ops = getSafeFileOps();

  // Check if config file exists and is readable
  if (!safe_ops.fileExists(config_path_)) {
    log_handler_->error("Configuration file does not exist: {}", config_path_);
    return false;
  }

  // Try to parse the configuration
  auto content = safe_ops.readFileContent(config_path_);
  if (!content) {
    log_handler_->error("Failed to read configuration file: {}", config_path_);
    return false;
  }

  try {
    nlohmann::json test_config = nlohmann::json::parse(*content);

    // Validate the parsed configuration
    std::lock_guard<std::mutex> lock(mutex_);
    nlohmann::json original_config = config_;
    const_cast<ConfigManager*>(this)->config_ = test_config;
    bool valid = validateConfig();
    const_cast<ConfigManager*>(this)->config_ = original_config;

    return valid;

  } catch (const std::exception& e) {
    log_handler_->error("Configuration file contains invalid JSON: {}", e.what());
    return false;
  }
}

bool ConfigManager::repairConfig() {
  log_handler_->info("Attempting to repair configuration");

  // Try to restore from the most recent backup
  auto backups = listBackups();
  for (const auto& backup : backups) {
    log_handler_->info("Trying to restore from backup: {}", backup);

    if (restoreFromBackup(backup)) {
      if (verifyConfigIntegrity()) {
        log_handler_->info("Configuration successfully repaired from backup: {}", backup);
        return true;
      } else {
        log_handler_->warn("Backup {} is also corrupted, trying next backup", backup);
      }
    }
  }

  // If no backup works, create a minimal default configuration
  log_handler_->warn("No valid backup found, creating minimal default configuration");
  return createDefaultConfig();
}

bool ConfigManager::createDefaultConfig() {
  nlohmann::json default_config = {
    {"data_upload_api", {
      {"base_url", "https://api.caby.care"},
      {"auth_token", ""}
    }},
    {"mediamtx_api", {
      {"base_url", "http://localhost:9997"}
    }},
    {"wifi", {
      {"ssid", ""},
      {"password", ""},
      {"is_valid", false}
    }},
    {"hardware_sn", ""},
    {"device_id", ""},
    {"user_id", ""},
    {"version_id", "1.0.0"},
    {"config_version", "1.0"}
  };

  auto& safe_ops = getSafeFileOps();
  auto result = safe_ops.writeFileAtomic(config_path_, default_config.dump(2));

  if (result == FileOpResult::SUCCESS) {
    std::lock_guard<std::mutex> lock(mutex_);
    config_ = default_config;
    log_handler_->info("Default configuration created successfully");
    return true;
  } else {
    log_handler_->error("Failed to create default configuration: {}",
                       SafeFileOps::getErrorString(result));
    return false;
  }
}

std::string ConfigManager::getConfigVersion() const {
  return getConfigValue<std::string>("config_version", "1.0");
}

bool ConfigManager::upgradeConfig(const std::string& from_version, const std::string& to_version) {
  log_handler_->info("Upgrading configuration from version {} to {}", from_version, to_version);

  // Create backup before upgrade
  if (!createBackup()) {
    log_handler_->error("Failed to create backup before config upgrade");
    return false;
  }

  // Perform version-specific upgrades
  if (from_version == "1.0" && to_version == "1.1") {
    // Example upgrade: add new fields
    std::lock_guard<std::mutex> lock(mutex_);
    if (!config_.contains("system_monitor")) {
      config_["system_monitor"] = {
        {"enabled", true},
        {"interval_seconds", 30}
      };
    }
  }

  // Update config version
  if (!setConfigValue("config_version", to_version)) {
    log_handler_->error("Failed to update config version");
    return false;
  }

  log_handler_->info("Configuration upgrade completed successfully");
  return true;
}

} // namespace aby_box