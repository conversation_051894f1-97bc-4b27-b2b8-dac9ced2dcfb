// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "utils/safe_file_ops.hpp"
#include <random>
#include <sstream>
#include <iomanip>
#include <fcntl.h>
#include <unistd.h>
#include <sys/statvfs.h>
#include <openssl/md5.h>
#include <openssl/sha.h>
#include <unistd.h>
#include <iomanip>

namespace aby_box {

SafeFileOps::SafeFileOps() : Loggable("SafeFileOps") {
  log_handler_->info("SafeFileOps initialized");
}

FileOpResult SafeFileOps::writeFileAtomic(const std::string& path, const std::string& content) {
  std::lock_guard<std::mutex> lock(ops_mutex_);
  
  try {
    // Check if we have enough disk space
    if (!hasEnoughSpace(path, content.size() * 2)) { // 2x for safety
      log_handler_->error("Insufficient disk space for writing file: {}", path);
      return FileOpResult::DISK_FULL;
    }
    
    // Create temporary file
    std::string temp_path = path + ".tmp." + std::to_string(std::time(nullptr));
    
    // Write to temporary file first
    {
      std::ofstream temp_file(temp_path, std::ios::binary);
      if (!temp_file.is_open()) {
        log_handler_->error("Failed to create temporary file: {}", temp_path);
        return FileOpResult::IO_ERROR;
      }
      
      temp_file << content;
      temp_file.flush();
      
      if (temp_file.fail()) {
        log_handler_->error("Failed to write to temporary file: {}", temp_path);
        std::filesystem::remove(temp_path);
        return FileOpResult::IO_ERROR;
      }
    }
    
    // Sync to disk
    sync();
    
    // Atomic move
    std::error_code ec;
    std::filesystem::rename(temp_path, path, ec);
    if (ec) {
      log_handler_->error("Failed to rename temporary file {} to {}: {}", 
                         temp_path, path, ec.message());
      std::filesystem::remove(temp_path);
      return handleFileSystemError(std::filesystem::filesystem_error("rename", ec));
    }
    
    log_handler_->debug("Successfully wrote file atomically: {}", path);
    return FileOpResult::SUCCESS;
    
  } catch (const std::filesystem::filesystem_error& e) {
    return handleFileSystemError(e);
  } catch (const std::exception& e) {
    return handleStdError(e);
  }
}

FileOpResult SafeFileOps::writeFileAtomic(const std::string& path, const std::vector<uint8_t>& data) {
  std::lock_guard<std::mutex> lock(ops_mutex_);
  
  try {
    if (!hasEnoughSpace(path, data.size() * 2)) {
      return FileOpResult::DISK_FULL;
    }
    
    std::string temp_path = path + ".tmp." + std::to_string(std::time(nullptr));
    
    {
      std::ofstream temp_file(temp_path, std::ios::binary);
      if (!temp_file.is_open()) {
        return FileOpResult::IO_ERROR;
      }
      
      temp_file.write(reinterpret_cast<const char*>(data.data()), data.size());
      temp_file.flush();
      
      if (temp_file.fail()) {
        std::filesystem::remove(temp_path);
        return FileOpResult::IO_ERROR;
      }
    }
    
    sync();
    
    std::error_code ec;
    std::filesystem::rename(temp_path, path, ec);
    if (ec) {
      std::filesystem::remove(temp_path);
      return handleFileSystemError(std::filesystem::filesystem_error("rename", ec));
    }
    
    return FileOpResult::SUCCESS;
    
  } catch (const std::filesystem::filesystem_error& e) {
    return handleFileSystemError(e);
  } catch (const std::exception& e) {
    return handleStdError(e);
  }
}

std::optional<std::string> SafeFileOps::readFileContent(const std::string& path) {
  try {
    if (!fileExists(path)) {
      log_handler_->debug("File does not exist: {}", path);
      return std::nullopt;
    }
    
    std::ifstream file(path, std::ios::binary);
    if (!file.is_open()) {
      log_handler_->error("Failed to open file for reading: {}", path);
      return std::nullopt;
    }
    
    std::string content((std::istreambuf_iterator<char>(file)),
                       std::istreambuf_iterator<char>());
    
    if (file.bad()) {
      log_handler_->error("Error reading file: {}", path);
      return std::nullopt;
    }
    
    return content;
    
  } catch (const std::exception& e) {
    log_handler_->error("Exception reading file {}: {}", path, e.what());
    return std::nullopt;
  }
}

std::optional<std::vector<uint8_t>> SafeFileOps::readFileBinary(const std::string& path) {
  try {
    if (!fileExists(path)) {
      return std::nullopt;
    }
    
    std::ifstream file(path, std::ios::binary);
    if (!file.is_open()) {
      return std::nullopt;
    }
    
    file.seekg(0, std::ios::end);
    size_t size = file.tellg();
    file.seekg(0, std::ios::beg);
    
    std::vector<uint8_t> data(size);
    file.read(reinterpret_cast<char*>(data.data()), size);
    
    if (file.bad()) {
      return std::nullopt;
    }
    
    return data;
    
  } catch (const std::exception& e) {
    log_handler_->error("Exception reading binary file {}: {}", path, e.what());
    return std::nullopt;
  }
}

FileOpResult SafeFileOps::copyFile(const std::string& source, const std::string& destination, 
                                  bool verify_checksum) {
  try {
    if (!fileExists(source)) {
      return FileOpResult::FILE_NOT_FOUND;
    }
    
    auto source_size = getFileSize(source);
    if (!source_size) {
      return FileOpResult::IO_ERROR;
    }
    
    if (!hasEnoughSpace(destination, *source_size)) {
      return FileOpResult::DISK_FULL;
    }
    
    // Calculate source checksum if verification is requested
    std::optional<std::string> source_checksum;
    if (verify_checksum) {
      source_checksum = calculateChecksum(source);
      if (!source_checksum) {
        log_handler_->warn("Failed to calculate source checksum, proceeding without verification");
        verify_checksum = false;
      }
    }
    
    // Copy file
    std::error_code ec;
    std::filesystem::copy_file(source, destination, 
                              std::filesystem::copy_options::overwrite_existing, ec);
    if (ec) {
      return handleFileSystemError(std::filesystem::filesystem_error("copy_file", ec));
    }
    
    // Verify checksum if requested
    if (verify_checksum && source_checksum) {
      if (!verifyChecksum(destination, *source_checksum)) {
        log_handler_->error("Checksum verification failed for copied file: {}", destination);
        std::filesystem::remove(destination);
        return FileOpResult::IO_ERROR;
      }
    }
    
    return FileOpResult::SUCCESS;
    
  } catch (const std::filesystem::filesystem_error& e) {
    return handleFileSystemError(e);
  } catch (const std::exception& e) {
    return handleStdError(e);
  }
}

bool SafeFileOps::fileExists(const std::string& path) const {
  std::error_code ec;
  return std::filesystem::exists(path, ec) && !ec;
}

bool SafeFileOps::isDirectory(const std::string& path) const {
  std::error_code ec;
  return std::filesystem::is_directory(path, ec) && !ec;
}

std::optional<size_t> SafeFileOps::getFileSize(const std::string& path) const {
  try {
    std::error_code ec;
    auto size = std::filesystem::file_size(path, ec);
    if (ec) {
      return std::nullopt;
    }
    return size;
  } catch (const std::exception&) {
    return std::nullopt;
  }
}

std::optional<size_t> SafeFileOps::getAvailableSpace(const std::string& path) const {
  try {
    struct statvfs stat;
    if (statvfs(path.c_str(), &stat) == 0) {
      return stat.f_bavail * stat.f_frsize;
    }
    return std::nullopt;
  } catch (const std::exception&) {
    return std::nullopt;
  }
}

bool SafeFileOps::hasEnoughSpace(const std::string& path, size_t required_bytes) const {
  auto available = getAvailableSpace(path);
  if (!available) {
    return false;
  }
  
  // Add 10% safety margin
  size_t required_with_margin = required_bytes + (required_bytes / 10);
  return *available >= required_with_margin;
}

FileOpResult SafeFileOps::handleFileSystemError(const std::filesystem::filesystem_error& e) const {
  auto ec = e.code();
  
  if (ec == std::errc::no_such_file_or_directory) {
    return FileOpResult::FILE_NOT_FOUND;
  } else if (ec == std::errc::permission_denied) {
    return FileOpResult::PERMISSION_DENIED;
  } else if (ec == std::errc::no_space_on_device) {
    return FileOpResult::DISK_FULL;
  } else {
    log_handler_->error("Filesystem error: {}", e.what());
    return FileOpResult::IO_ERROR;
  }
}

FileOpResult SafeFileOps::handleStdError(const std::exception& e) const {
  log_handler_->error("Standard exception: {}", e.what());
  return FileOpResult::UNKNOWN_ERROR;
}

std::string SafeFileOps::getErrorString(FileOpResult result) {
  switch (result) {
    case FileOpResult::SUCCESS: return "Success";
    case FileOpResult::FILE_NOT_FOUND: return "File not found";
    case FileOpResult::PERMISSION_DENIED: return "Permission denied";
    case FileOpResult::DISK_FULL: return "Disk full";
    case FileOpResult::IO_ERROR: return "I/O error";
    case FileOpResult::INVALID_PATH: return "Invalid path";
    case FileOpResult::TIMEOUT: return "Timeout";
    case FileOpResult::UNKNOWN_ERROR: return "Unknown error";
    default: return "Unrecognized error";
  }
}

FileOpResult SafeFileOps::moveFile(const std::string& source, const std::string& destination) {
  try {
    if (!fileExists(source)) {
      return FileOpResult::FILE_NOT_FOUND;
    }

    std::error_code ec;
    std::filesystem::rename(source, destination, ec);
    if (ec) {
      return handleFileSystemError(std::filesystem::filesystem_error("rename", ec));
    }

    return FileOpResult::SUCCESS;

  } catch (const std::filesystem::filesystem_error& e) {
    return handleFileSystemError(e);
  } catch (const std::exception& e) {
    return handleStdError(e);
  }
}

FileOpResult SafeFileOps::createDirectories(const std::string& path) {
  try {
    std::error_code ec;
    std::filesystem::create_directories(path, ec);
    if (ec) {
      return handleFileSystemError(std::filesystem::filesystem_error("create_directories", ec));
    }
    return FileOpResult::SUCCESS;
  } catch (const std::filesystem::filesystem_error& e) {
    return handleFileSystemError(e);
  } catch (const std::exception& e) {
    return handleStdError(e);
  }
}

bool SafeFileOps::isWritable(const std::string& path) const {
  return access(path.c_str(), W_OK) == 0;
}

std::optional<std::string> SafeFileOps::calculateChecksum(const std::string& path, const std::string& algorithm) const {
  try {
    auto data = readFileBinary(path);
    if (!data) {
      return std::nullopt;
    }

    if (algorithm == "md5") {
      unsigned char hash[MD5_DIGEST_LENGTH];
      MD5(data->data(), data->size(), hash);

      std::stringstream ss;
      for (int i = 0; i < MD5_DIGEST_LENGTH; ++i) {
        ss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(hash[i]);
      }
      return ss.str();
    }

    return std::nullopt;
  } catch (const std::exception& e) {
    log_handler_->error("Failed to calculate checksum: {}", e.what());
    return std::nullopt;
  }
}

bool SafeFileOps::verifyChecksum(const std::string& path, const std::string& expected_checksum,
                                const std::string& algorithm) const {
  auto actual_checksum = calculateChecksum(path, algorithm);
  return actual_checksum && (*actual_checksum == expected_checksum);
}

SafeFileOps& getSafeFileOps() {
  static SafeFileOps instance;
  return instance;
}

} // namespace aby_box
