// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "utils/system_monitor.hpp"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <filesystem>
#include <sys/sysinfo.h>
#include <unistd.h>
#include <dirent.h>

namespace aby_box {

SystemMonitor::SystemMonitor() : Loggable("SystemMonitor") {
  log_handler_->info("SystemMonitor initialized");
}

SystemMonitor::~SystemMonitor() {
  stopMonitoring();
}

SystemMonitor& SystemMonitor::getInstance() {
  static SystemMonitor instance;
  return instance;
}

void SystemMonitor::startMonitoring(std::chrono::milliseconds interval) {
  if (monitoring_active_.exchange(true)) {
    log_handler_->warn("System monitoring already active");
    return;
  }
  
  monitoring_thread_ = std::make_unique<std::thread>([this, interval]() {
    monitoringLoop();
  });
  
  log_handler_->info("System monitoring started with interval: {}ms", interval.count());
}

void SystemMonitor::stopMonitoring() {
  if (!monitoring_active_.exchange(false)) {
    return;
  }
  
  if (monitoring_thread_ && monitoring_thread_->joinable()) {
    monitoring_thread_->join();
  }
  monitoring_thread_.reset();
  
  log_handler_->info("System monitoring stopped");
}

SystemMetrics SystemMonitor::getCurrentMetrics() const {
  std::lock_guard<std::mutex> lock(metrics_mutex_);
  return last_metrics_;
}

OtaMetrics SystemMonitor::getCurrentOtaMetrics() const {
  std::lock_guard<std::mutex> lock(metrics_mutex_);
  return ota_metrics_;
}

void SystemMonitor::addAlert(AlertLevel level, const std::string& category, 
                            const std::string& message, const std::string& details) {
  SystemAlert alert;
  alert.level = level;
  alert.category = category;
  alert.message = message;
  alert.details = details;
  alert.timestamp = std::chrono::system_clock::now();
  
  {
    std::lock_guard<std::mutex> lock(alerts_mutex_);
    alerts_.push_back(alert);
  }
  
  // Log the alert
  switch (level) {
    case AlertLevel::INFO:
      log_handler_->info("ALERT [{}]: {}", category, message);
      break;
    case AlertLevel::WARNING:
      log_handler_->warn("ALERT [{}]: {}", category, message);
      break;
    case AlertLevel::CRITICAL:
      log_handler_->error("CRITICAL ALERT [{}]: {}", category, message);
      break;
    case AlertLevel::EMERGENCY:
      log_handler_->error("EMERGENCY ALERT [{}]: {}", category, message);
      break;
  }
  
  // Trigger callbacks for critical alerts
  if (level >= AlertLevel::CRITICAL) {
    triggerAlert(alert);
  }
}

void SystemMonitor::startOtaMonitoring(const std::string& version) {
  std::lock_guard<std::mutex> lock(metrics_mutex_);
  
  ota_metrics_.ota_in_progress = true;
  ota_metrics_.ota_version = version;
  ota_metrics_.ota_status = "starting";
  ota_metrics_.ota_progress_percent = 0.0;
  ota_metrics_.ota_start_time = std::chrono::system_clock::now();
  ota_metrics_.network_interruptions = 0;
  ota_metrics_.peak_memory_usage = 0.0;
  ota_metrics_.peak_cpu_usage = 0.0;
  
  log_handler_->info("Started OTA monitoring for version: {}", version);
  addAlert(AlertLevel::INFO, "OTA", "OTA upgrade started", "Version: " + version);
}

void SystemMonitor::updateOtaProgress(double progress_percent, const std::string& status) {
  std::lock_guard<std::mutex> lock(metrics_mutex_);
  
  ota_metrics_.ota_progress_percent = progress_percent;
  ota_metrics_.ota_status = status;
  ota_metrics_.ota_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
      std::chrono::system_clock::now() - ota_metrics_.ota_start_time);
  
  log_handler_->debug("OTA progress: {:.1f}% - {}", progress_percent, status);
}

void SystemMonitor::stopOtaMonitoring(bool success) {
  std::lock_guard<std::mutex> lock(metrics_mutex_);
  
  ota_metrics_.ota_in_progress = false;
  ota_metrics_.ota_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
      std::chrono::system_clock::now() - ota_metrics_.ota_start_time);
  
  if (success) {
    ota_metrics_.ota_status = "completed";
    log_handler_->info("OTA monitoring completed successfully for version: {}", ota_metrics_.ota_version);
    addAlert(AlertLevel::INFO, "OTA", "OTA upgrade completed successfully", 
             "Version: " + ota_metrics_.ota_version);
  } else {
    ota_metrics_.ota_status = "failed";
    log_handler_->error("OTA monitoring completed with failure for version: {}", ota_metrics_.ota_version);
    addAlert(AlertLevel::CRITICAL, "OTA", "OTA upgrade failed", 
             "Version: " + ota_metrics_.ota_version);
    
    if (ota_failure_callback_) {
      ota_failure_callback_(ota_metrics_.ota_version);
    }
  }
}

void SystemMonitor::monitoringLoop() {
  while (monitoring_active_) {
    try {
      // Collect system metrics
      auto metrics = collectSystemMetrics();
      
      {
        std::lock_guard<std::mutex> lock(metrics_mutex_);
        last_metrics_ = metrics;
        
        // Update OTA peak usage if OTA is in progress
        if (ota_metrics_.ota_in_progress) {
          ota_metrics_.peak_memory_usage = std::max(ota_metrics_.peak_memory_usage, 
                                                   metrics.memory_usage_percent);
          ota_metrics_.peak_cpu_usage = std::max(ota_metrics_.peak_cpu_usage, 
                                                metrics.cpu_usage_percent);
        }
      }
      
      // Check system health
      checkSystemHealth(metrics);
      
      // Check OTA health if OTA is in progress
      if (ota_metrics_.ota_in_progress) {
        checkOtaHealth();
      }
      
      // Clean up old alerts
      clearOldAlerts();
      
    } catch (const std::exception& e) {
      log_handler_->error("Exception in monitoring loop: {}", e.what());
    }
    
    std::this_thread::sleep_for(std::chrono::seconds(30));
  }
}

SystemMetrics SystemMonitor::collectSystemMetrics() {
  SystemMetrics metrics;
  metrics.timestamp = std::chrono::system_clock::now();
  
  // CPU metrics
  metrics.cpu_usage_percent = getCpuUsage();
  getLoadAverages(metrics.load_average_1min, metrics.load_average_5min, metrics.load_average_15min);
  
  // Memory metrics
  getMemoryInfo(metrics.total_memory_kb, metrics.available_memory_kb, metrics.used_memory_kb);
  if (metrics.total_memory_kb > 0) {
    metrics.memory_usage_percent = (double)metrics.used_memory_kb / metrics.total_memory_kb * 100.0;
  }
  
  // Process metrics
  getProcessInfo(metrics.process_memory_kb, metrics.process_cpu_percent, 
                metrics.thread_count, metrics.fd_count);
  
  // Disk metrics
  getDiskInfo(metrics.disk_total_kb, metrics.disk_available_kb, metrics.disk_used_kb);
  if (metrics.disk_total_kb > 0) {
    metrics.disk_usage_percent = (double)metrics.disk_used_kb / metrics.disk_total_kb * 100.0;
  }
  
  // Network metrics
  metrics.network_connected = getNetworkStatus(metrics.network_interface);
  
  // Temperature
  metrics.cpu_temperature = getCpuTemperature();
  
  return metrics;
}

double SystemMonitor::getCpuUsage() {
  static unsigned long long last_total = 0, last_idle = 0;
  
  std::ifstream stat_file("/proc/stat");
  std::string line;
  if (std::getline(stat_file, line)) {
    std::istringstream iss(line);
    std::string cpu;
    unsigned long long user, nice, system, idle, iowait, irq, softirq, steal;
    
    iss >> cpu >> user >> nice >> system >> idle >> iowait >> irq >> softirq >> steal;
    
    unsigned long long total = user + nice + system + idle + iowait + irq + softirq + steal;
    unsigned long long total_diff = total - last_total;
    unsigned long long idle_diff = idle - last_idle;
    
    double cpu_percent = 0.0;
    if (total_diff > 0) {
      cpu_percent = 100.0 * (total_diff - idle_diff) / total_diff;
    }
    
    last_total = total;
    last_idle = idle;
    
    return cpu_percent;
  }
  
  return 0.0;
}

void SystemMonitor::getMemoryInfo(size_t& total, size_t& available, size_t& used) {
  struct sysinfo si;
  if (sysinfo(&si) == 0) {
    total = si.totalram * si.mem_unit / 1024;
    available = si.freeram * si.mem_unit / 1024;
    used = total - available;
  }
}

bool SystemMonitor::getNetworkStatus(std::string& interface) {
  // Check if any network interface is up and has an IP
  std::ifstream route_file("/proc/net/route");
  std::string line;
  
  while (std::getline(route_file, line)) {
    if (line.find("00000000") != std::string::npos) { // Default route
      std::istringstream iss(line);
      iss >> interface;
      return true;
    }
  }
  
  return false;
}

void SystemMonitor::checkSystemHealth(const SystemMetrics& metrics) {
  // Check CPU usage
  if (metrics.cpu_usage_percent > CPU_CRITICAL_THRESHOLD) {
    addAlert(AlertLevel::CRITICAL, "CPU", "Critical CPU usage", 
             "CPU usage: " + std::to_string(metrics.cpu_usage_percent) + "%");
  } else if (metrics.cpu_usage_percent > CPU_WARNING_THRESHOLD) {
    addAlert(AlertLevel::WARNING, "CPU", "High CPU usage", 
             "CPU usage: " + std::to_string(metrics.cpu_usage_percent) + "%");
  }
  
  // Check memory usage
  if (metrics.memory_usage_percent > MEMORY_CRITICAL_THRESHOLD) {
    addAlert(AlertLevel::CRITICAL, "Memory", "Critical memory usage", 
             "Memory usage: " + std::to_string(metrics.memory_usage_percent) + "%");
  } else if (metrics.memory_usage_percent > MEMORY_WARNING_THRESHOLD) {
    addAlert(AlertLevel::WARNING, "Memory", "High memory usage", 
             "Memory usage: " + std::to_string(metrics.memory_usage_percent) + "%");
  }
  
  // Check disk usage
  if (metrics.disk_usage_percent > DISK_CRITICAL_THRESHOLD) {
    addAlert(AlertLevel::CRITICAL, "Disk", "Critical disk usage", 
             "Disk usage: " + std::to_string(metrics.disk_usage_percent) + "%");
  } else if (metrics.disk_usage_percent > DISK_WARNING_THRESHOLD) {
    addAlert(AlertLevel::WARNING, "Disk", "High disk usage", 
             "Disk usage: " + std::to_string(metrics.disk_usage_percent) + "%");
  }
  
  // Check network connectivity
  if (!metrics.network_connected) {
    addAlert(AlertLevel::CRITICAL, "Network", "Network disconnected", "No network connectivity");
  }
}

void SystemMonitor::triggerAlert(const SystemAlert& alert) {
  if (system_critical_callback_ && alert.level >= AlertLevel::CRITICAL) {
    try {
      system_critical_callback_(alert);
    } catch (const std::exception& e) {
      log_handler_->error("Exception in system critical callback: {}", e.what());
    }
  }
}

SystemMonitor& getSystemMonitor() {
  return SystemMonitor::getInstance();
}

} // namespace aby_box
