// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "utils/thread_safety.hpp"
#include <pthread.h>
#include <algorithm>

namespace aby_box {

// DeadlockDetector implementation
DeadlockDetector::DeadlockDetector() : Loggable("DeadlockDetector") {
  log_handler_->info("DeadlockDetector initialized");
}

DeadlockDetector& DeadlockDetector::getInstance() {
  static DeadlockDetector instance;
  return instance;
}

void DeadlockDetector::registerLockAcquisition(const void* mutex_ptr, const std::string& location) {
  if (!enabled_.load()) return;
  
  std::lock_guard<std::mutex> lock(detector_mutex_);
  
  auto thread_id = std::this_thread::get_id();
  
  // Check if this thread already holds this mutex
  auto& thread_mutexes = thread_locks_[thread_id];
  if (thread_mutexes.find(mutex_ptr) != thread_mutexes.end()) {
    log_handler_->warn("Thread {} attempting to acquire mutex {} it already holds at {}", 
                      std::hash<std::thread::id>{}(thread_id), mutex_ptr, location);
    return;
  }
  
  // Record the lock acquisition
  LockInfo info;
  info.thread_id = thread_id;
  info.location = location;
  info.acquisition_time = std::chrono::steady_clock::now();
  
  active_locks_[mutex_ptr] = info;
  thread_mutexes.insert(mutex_ptr);
  
  log_handler_->debug("Thread {} acquired mutex {} at {}", 
                     std::hash<std::thread::id>{}(thread_id), mutex_ptr, location);
}

void DeadlockDetector::registerLockRelease(const void* mutex_ptr) {
  if (!enabled_.load()) return;
  
  std::lock_guard<std::mutex> lock(detector_mutex_);
  
  auto thread_id = std::this_thread::get_id();
  
  // Remove from active locks
  active_locks_.erase(mutex_ptr);
  
  // Remove from thread's mutex set
  auto it = thread_locks_.find(thread_id);
  if (it != thread_locks_.end()) {
    it->second.erase(mutex_ptr);
    if (it->second.empty()) {
      thread_locks_.erase(it);
    }
  }
  
  log_handler_->debug("Thread {} released mutex {}", 
                     std::hash<std::thread::id>{}(thread_id), mutex_ptr);
}

bool DeadlockDetector::checkForDeadlock() {
  if (!enabled_.load()) return false;
  
  std::lock_guard<std::mutex> lock(detector_mutex_);
  
  // Simple deadlock detection: check for circular wait
  for (const auto& [thread_id, mutexes] : thread_locks_) {
    if (mutexes.size() > 1) {
      log_handler_->warn("Thread {} holds {} mutexes simultaneously", 
                        std::hash<std::thread::id>{}(thread_id), mutexes.size());
      
      // Check for long-held locks
      for (const void* mutex_ptr : mutexes) {
        auto lock_it = active_locks_.find(mutex_ptr);
        if (lock_it != active_locks_.end()) {
          auto duration = std::chrono::steady_clock::now() - lock_it->second.acquisition_time;
          if (duration > std::chrono::seconds(10)) {
            log_handler_->error("Potential deadlock: mutex {} held for {}ms at {}", 
                               mutex_ptr, 
                               std::chrono::duration_cast<std::chrono::milliseconds>(duration).count(),
                               lock_it->second.location);
            return true;
          }
        }
      }
    }
  }
  
  return false;
}

// ThreadPool implementation
ThreadPool::ThreadPool(size_t num_threads, const std::string& name) 
  : Loggable("ThreadPool"), pool_name_(name) {
  
  workers_.reserve(num_threads);
  
  for (size_t i = 0; i < num_threads; ++i) {
    workers_.emplace_back(&ThreadPool::workerLoop, this);
    
    // Set thread name
    std::string thread_name = pool_name_ + "_" + std::to_string(i);
    pthread_setname_np(workers_.back().native_handle(), thread_name.c_str());
  }
  
  log_handler_->info("ThreadPool '{}' created with {} threads", name, num_threads);
}

ThreadPool::~ThreadPool() {
  shutdown();
}

void ThreadPool::shutdown(std::chrono::milliseconds timeout) {
  log_handler_->info("Shutting down ThreadPool '{}'", pool_name_);
  
  {
    std::lock_guard<std::mutex> lock(queue_mutex_);
    shutdown_ = true;
  }
  condition_.notify_all();
  
  auto start_time = std::chrono::steady_clock::now();
  
  for (auto& worker : workers_) {
    if (worker.joinable()) {
      auto remaining_time = timeout - std::chrono::duration_cast<std::chrono::milliseconds>(
          std::chrono::steady_clock::now() - start_time);
      
      if (remaining_time > std::chrono::milliseconds(0)) {
        // Try to join with timeout (simplified approach)
        worker.join();
      } else {
        log_handler_->warn("Thread in pool '{}' did not shutdown in time", pool_name_);
        worker.detach();
      }
    }
  }
  
  log_handler_->info("ThreadPool '{}' shutdown completed", pool_name_);
}

void ThreadPool::workerLoop() {
  active_threads_++;
  
  while (true) {
    std::function<void()> task;
    
    {
      std::unique_lock<std::mutex> lock(queue_mutex_);
      condition_.wait(lock, [this] { return shutdown_ || !tasks_.empty(); });
      
      if (shutdown_ && tasks_.empty()) {
        break;
      }
      
      if (!tasks_.empty()) {
        task = std::move(tasks_.front());
        tasks_.pop();
      }
    }
    
    if (task) {
      try {
        task();
      } catch (const std::exception& e) {
        log_handler_->error("Exception in ThreadPool '{}' task: {}", pool_name_, e.what());
      } catch (...) {
        log_handler_->error("Unknown exception in ThreadPool '{}' task", pool_name_);
      }
    }
  }
  
  active_threads_--;
}

// ThreadNamer implementation
void ThreadNamer::setThreadName(const std::string& name) {
  pthread_setname_np(pthread_self(), name.c_str());
}

std::string ThreadNamer::getThreadName() {
  char name[16];
  if (pthread_getname_np(pthread_self(), name, sizeof(name)) == 0) {
    return std::string(name);
  }
  return "unknown";
}

void ThreadNamer::setThreadName(std::thread& thread, const std::string& name) {
  pthread_setname_np(thread.native_handle(), name.c_str());
}

} // namespace aby_box
