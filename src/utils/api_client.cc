#include "utils/api_client.h"
#include "utils/config_manager.hpp"
#include "module/ota/ota_manager.hpp" // Add include for OTA manager
#include "module/wifi.hpp" // Add include for WiFi module
#include <filesystem>
#include <fstream>
#include <functional>
#include <random>

namespace aby_box {

// Static function implementations
size_t WriteCallback(void *contents, size_t size, size_t nmemb,
                     std::string *userp) {
  userp->append((char *)contents, size * nmemb);
  return size * nmemb;
}

int executeCommand(const std::string &cmd) { return system(cmd.c_str()); }

// Singleton instance getter
APIClient &APIClient::getInstance() {
  static APIClient instance;
  return instance;
}

// Constructor
APIClient::APIClient() {}

bool APIClient::init() {
  log_handler_ = std::make_shared<LogHandler>("APIClient");

  // Read configuration
  loadConfigValues();

  curl_global_init(CURL_GLOBAL_ALL);
  return true;
}

void APIClient::cleanup() {
  should_exit_ = true;

  if (stop_thread_.joinable()) {
    stop_thread_.join();
  }
  curl_global_cleanup();
}
bool APIClient::startRecording(uint64_t timestamp) {
  if (is_recording_) {
    spdlog::info("Recording is already in progress");
    return false;
  }

  if (stop_pending_) {
    stop_request_time_ = 0;
    stop_pending_ = false;
    spdlog::info("Cancelled pending stop recording request");
  }

  // Execute ffmpeg.sh start command
  int result = executeCommand("bash /etc/tools/ffmpeg.sh start " +
                              std::to_string(timestamp));
  spdlog::info("Recording started with timestamp: {}", timestamp);
  if (result == 0) {
    is_recording_ = true;
    current_recording_timestamp_ = timestamp; // 保存当前录制的时间戳
    spdlog::info("Recording started successfully");
    return true;
  }

  spdlog::error("Failed to start recording, ffmpeg.sh returned: {}", result);
  return false;
}

bool APIClient::stopRecording() {
  if (!is_recording_) {
    spdlog::info("No active recording to stop");
    return false;
  }
  if (should_exit_) {
    return false;
  }
  // Execute ffmpeg.sh stop command
  int result = executeCommand("bash /etc/tools/ffmpeg.sh stop");
  if (result == 0) {
    is_recording_ = false;
    // 清理录制时间戳，防止其他模块获取到过期的时间戳
    spdlog::info("Recording stopped successfully");
  } else {
    spdlog::error("Failed to stop recording, ffmpeg.sh returned: {}", result);
  }
  return true;
}



std::string APIClient::get_ip_timezone() {
  std::string response;
  CURLcode res = performSimpleGetRequest("http://ip-api.com/json/", response);

  if (res != CURLE_OK || response.empty()) {
    log_handler_->error("Failed to get timezone data");
    return "";
  }

  log_handler_->debug("API Response: {}", response);

  try {
    auto j = nlohmann::json::parse(response);

    if (!j.is_object()) {
      log_handler_->error("Invalid JSON response: not an object");
      return "";
    }

    if (j.contains("status") && j["status"] != "success") {
      log_handler_->error("API error: {}", j.contains("message")
                                               ? j["message"].get<std::string>()
                                               : "unknown error");
      return "";
    }

    if (!j.contains("timezone") || !j["timezone"].is_string()) {
      log_handler_->error("Missing or invalid timezone field in response");
      return "";
    }

    std::string timezone = j["timezone"].get<std::string>();
    log_handler_->debug("Successfully parsed timezone: {}", timezone);
    return timezone;

  } catch (const std::exception &e) {
    log_handler_->error("Error parsing response: {}", e.what());
  }

  return "";
}

// Helper methods implementation
std::string APIClient::readFromFile(const std::string &path) {
  std::ifstream file(path);
  std::string content;
  if (file.is_open()) {
    std::getline(file, content);
    file.close();
  }
  return content;
}

// 实时获取设备信息的方法
std::string APIClient::get_device_id() const {
  auto &config = ConfigManager::getInstance();
  return config.getDeviceId();
}

std::string APIClient::get_user_id() const {
  auto &config = ConfigManager::getInstance();
  return config.getUserId();
}

std::string APIClient::get_hardware_sn() const {
  auto &config = ConfigManager::getInstance();
  return config.getHardwareSn();
}

void APIClient::loadConfigValues() {
  auto &config = ConfigManager::getInstance();
  device_id_ = config.getConfigValue<std::string>("device_id", "");
  user_id_ = config.getConfigValue<std::string>("user_id", "");
  hardware_sn_ = config.getConfigValue<std::string>("hardware_sn", "");
  base_url_ = config.getmediamtxUrl();
}

CURLcode APIClient::performSimpleGetRequest(const std::string &url,
                                            std::string &response) {
  return performRequestWithRetry(url, "GET", "", response, nullptr, 1);
}

CURLcode APIClient::performCurlRequest(const std::string &url,
                                       const std::string &method,
                                       const std::string &postData,
                                       std::string &response, long *http_code) {
  return performRequestWithRetry(url, method, postData, response, http_code, 3);
}

bool APIClient::createVideoRecord(int64_t timestamp_enter,
                                  int64_t timestamp_leave, double weight_litter,
                                  double weight_cat, double weight_waste) {
  std::string user_id = get_user_id();
  std::string device_id = get_device_id();
  
  if (user_id.empty() || device_id.empty()) {
    log_handler_->error(
        "Cannot create video record: user_id or device_id is empty");
    return false;
  }

  std::string api_url = base_url_ + "/api/records";
  std::string response;

  // Get current timestamp in ISO8601 format, timestamp_enter and
  // timestamp_leave are in seconds
  std::time_t ts_start = timestamp_enter;  // 不需要除以1000
  std::time_t ts_end = timestamp_leave;    // 不需要除以1000

  std::tm now_tm_start = *std::gmtime(&ts_start);
  std::tm now_tm_end = *std::gmtime(&ts_end);
  char timestamp_start[30];
  char timestamp_end[30];
  std::strftime(timestamp_start, sizeof(timestamp_start), "%Y-%m-%dT%H:%M:%SZ",
                &now_tm_start);
  std::strftime(timestamp_end, sizeof(timestamp_end), "%Y-%m-%dT%H:%M:%SZ",
                &now_tm_end);

  // 生成基于设备ID和时间戳的唯一视频ID
  // 获取设备ID的前8位和当前毫秒级时间戳
  std::string device_prefix =
      device_id.substr(0, std::min(size_t(8), device_id.length()));

  std::string video_id = "v" + device_prefix + std::to_string(timestamp_leave);
  log_handler_->debug("Generated unique video ID: {}", video_id);

  // Build request body as JSON
  nlohmann::json request_body = {{"user_id", user_id},
                                 {"record",
                                  {{"device_id", device_id},
                                   {"video_id", video_id},
                                   {"start_time", timestamp_start},
                                   {"end_time", timestamp_end},
                                   {"weight_litter", weight_litter},
                                   {"weight_cat", weight_cat},
                                   {"weight_waste", weight_waste}}}};

  std::string request_data = request_body.dump();
  log_handler_->debug("Creating video record with data: {}", request_data);

  long http_code = 0;
  CURLcode res =
      performCurlRequest(api_url, "POST", request_data, response, &http_code);

  if (res != CURLE_OK) {
    log_handler_->error("Failed to create video record: {}",
                        curl_easy_strerror(res));
    return false;
  }

  if (http_code < 200 || http_code >= 300) {
    log_handler_->error("Server returned error code {}: {}", http_code,
                        response);
    return false;
  }

  try {
    auto json_response = nlohmann::json::parse(response);
    log_handler_->info("Video record created successfully with ID: {}",
                       json_response["record"]["video_id"].get<std::string>());
    return true;
  } catch (const std::exception &e) {
    log_handler_->error("Failed to parse API response: {}", e.what());
    return false;
  }
}

bool APIClient::send_device_timezone(const std::string& timezone) {
  std::string device_id = get_device_id();
  
  if (device_id.empty()) {
    log_handler_->error("Cannot send timezone: device_id is empty");
    return false;
  }

  std::string api_url = base_url_ + "/api/devices/timezone";
  std::string response;
  
  // Prepare the POST data
  std::string post_data = "device_id=" + device_id + "&timezone=" + timezone;
  
  log_handler_->info("Sending device timezone to server: {}", timezone);
  log_handler_->debug("POST data: {}", post_data);

  long http_code = 0;
  
  // Create a new CURL handle for this request
  CURL *curl = curl_easy_init();
  if (!curl) {
    log_handler_->error("Failed to initialize CURL handle");
    return false;
  }
  
  struct curl_slist *headers = NULL;
  headers = curl_slist_append(headers, "Content-Type: application/x-www-form-urlencoded");
  
  curl_easy_setopt(curl, CURLOPT_URL, api_url.c_str());
  curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
  curl_easy_setopt(curl, CURLOPT_POSTFIELDS, post_data.c_str());
  curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
  curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
  curl_easy_setopt(curl, CURLOPT_TIMEOUT, 5L);
  
  CURLcode res = curl_easy_perform(curl);
  curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &http_code);
  
  curl_slist_free_all(headers);
  curl_easy_cleanup(curl);
  
  if (res != CURLE_OK) {
    log_handler_->error("Failed to send timezone: {}", curl_easy_strerror(res));
    return false;
  }
  
  if (http_code != 200) {
    log_handler_->error("Server returned error code {}: {}", http_code, response);
    return false;
  }
  
  log_handler_->info("Device timezone sent successfully to server");
  return true;
}

nlohmann::json APIClient::sendHeartbeat() {
  std::string device_id = get_device_id();
  
  if (device_id.empty()) {
    log_handler_->error("Cannot send heartbeat: device_id is empty");
    return nlohmann::json({{"status", "error"}, {"message", "Device ID is empty"}});
  }

  std::string api_url = base_url_ + "/api/devices/heartbeat";
  std::string response;
  
  // Get WiFi signal strength
  int signal_strength = 0;
  std::string signal_cmd = "iw dev wlan0 link | grep signal | awk '{print $2}'";
  FILE* signal_file = popen(signal_cmd.c_str(), "r");
  if (signal_file) {
    char buffer[128];
    if (fgets(buffer, sizeof(buffer), signal_file) != NULL) {
      signal_strength = std::atoi(buffer);
    }
    pclose(signal_file);
  }
  log_handler_->debug("WiFi signal strength: {} dBm", signal_strength);
  
  // Get storage usage (percentage)
  int storage_usage = 0;
  std::string storage_cmd = "df -h | grep '/dev/root' | awk '{print $5}' | sed 's/%//'";
  FILE* storage_file = popen(storage_cmd.c_str(), "r");
  if (storage_file) {
    char buffer[128];
    if (fgets(buffer, sizeof(buffer), storage_file) != NULL) {
      storage_usage = std::atoi(buffer);
    }
    pclose(storage_file);
  }
  log_handler_->debug("Storage usage: {}%", storage_usage);
  
  // Get wlan0 IPv4 address
  std::string ipv4_address = "unknown";
  
  // First try to get IP from wlan0 (WiFi interface)
  std::string ip_cmd = "ip addr show wlan0 2>/dev/null | grep 'inet ' | awk '{print $2}' | cut -d'/' -f1";
  FILE* ip_file = popen(ip_cmd.c_str(), "r");
  if (ip_file) {
    char buffer[128];
    if (fgets(buffer, sizeof(buffer), ip_file) != NULL) {
      ipv4_address = std::string(buffer);
      // Remove trailing newline if present
      if (!ipv4_address.empty() && ipv4_address.back() == '\n') {
        ipv4_address.pop_back();
      }
    }
    pclose(ip_file);
  }
  
  // Final validation of IP address format
  if (ipv4_address.empty() || ipv4_address.find('.') == std::string::npos) {
    ipv4_address = "unknown";
  }
  
  log_handler_->debug("Device IPv4 address: {}", ipv4_address);
  
  // Get firmware version from config file
  std::string firmware_version = "v0.1.1"; // Default value
  try {
    std::ifstream config_file("/etc/cfg/aby_box/config.json");
    if (config_file.is_open()) {
      nlohmann::json config_json = nlohmann::json::parse(config_file);
      if (config_json.contains("version_id")) {
        firmware_version = config_json["version_id"].get<std::string>();
      }
      config_file.close();
    }
  } catch (const std::exception &e) {
    log_handler_->error("Error reading firmware version from config: {}", e.what());
  }
  log_handler_->debug("Firmware version: {}", firmware_version);
  
  // Sensor statuses
  bool camera_ok = true;
  bool mic_ok = true;
  bool weight_sensor_ok = true;
  bool temp_humid_sensor_ok = false;

  // Convert data to form-encoded format (what the server expects)
  std::string post_data = "device_id=" + device_id;
  post_data += "&status=1";
  post_data += "&timestamp=" + std::to_string(std::chrono::duration_cast<std::chrono::seconds>(
                  std::chrono::system_clock::now().time_since_epoch()).count());
  post_data += "&firmware_version=" + firmware_version;
  post_data += "&ipv4=" + ipv4_address;
  post_data += "&camera_ok=" + std::string(camera_ok ? "true" : "false");
  post_data += "&mic_ok=" + std::string(mic_ok ? "true" : "false");
  post_data += "&weight_sensor_ok=" + std::string(weight_sensor_ok ? "true" : "false");
  post_data += "&temp_humid_sensor_ok=" + std::string(temp_humid_sensor_ok ? "true" : "false");
  post_data += "&signal_strength=" + std::to_string(-signal_strength);
  post_data += "&storage_usage=" + std::to_string(storage_usage);

  log_handler_->debug("Sending heartbeat with form data: {}", post_data);

  long http_code = 0;
  
  // Create a new CURL handle for this request
  CURL *curl = curl_easy_init();
  if (!curl) {
    log_handler_->error("Failed to initialize CURL handle");
    return nlohmann::json({{"status", "error"}, {"message", "Failed to initialize CURL"}});
  }
  
  struct curl_slist *headers = NULL;
  headers = curl_slist_append(headers, "Content-Type: application/x-www-form-urlencoded");
  
  curl_easy_setopt(curl, CURLOPT_URL, api_url.c_str());
  curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
  curl_easy_setopt(curl, CURLOPT_POSTFIELDS, post_data.c_str());
  curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
  curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
  curl_easy_setopt(curl, CURLOPT_TIMEOUT, 5L);
  
  CURLcode res = curl_easy_perform(curl);
  curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &http_code);
  
  curl_slist_free_all(headers);
  curl_easy_cleanup(curl);

  if (res != CURLE_OK) {
    log_handler_->error("Failed to send heartbeat: {}", curl_easy_strerror(res));
    return nlohmann::json({{"status", "error"}, {"message", curl_easy_strerror(res)}});
  }

  if (http_code < 200 || http_code >= 300) {
    log_handler_->error("Server returned error code {}: {}", http_code, response);
    return nlohmann::json({{"status", "error"}, {"message", "HTTP error " + std::to_string(http_code)}});
  }

  try {
    // If response is empty or not valid JSON, create a simple success response
    if (response.empty()) {
      return nlohmann::json({{"status", "ok"}}); 
    }
    
    auto json_response = nlohmann::json::parse(response);
    log_handler_->debug("Heartbeat response: {}", json_response.dump());
    return json_response;
  } catch (const std::exception &e) {
    log_handler_->error("Failed to parse heartbeat response: {}", e.what());
    // If parsing fails but HTTP status was success, still return success
    if (http_code >= 200 && http_code < 300) {
      return nlohmann::json({{"status", "ok"}, {"raw_response", response}});
    }
    return nlohmann::json({{"status", "error"}, {"message", std::string("Parse error: ") + e.what()}});
  }
}

bool APIClient::updateOtaStatus(const std::string &status) {
  std::string device_id = get_device_id();
  
  if (device_id.empty()) {
    log_handler_->error("Cannot update OTA status: device_id is empty");
    return false;
  }

  // Validate status values according to API documentation
  if (status != "idle" && status != "updating" && status != "failed" && status != "completed") {
    log_handler_->error("Invalid OTA status value: {}. Must be one of: idle, updating, failed, completed", status);
    return false;
  }

  std::string api_url = base_url_ + "/api/devices/" + device_id + "/ota-status";
  std::string response;

  // Build request body as JSON according to API documentation
  nlohmann::json request_body = {
    {"device_id", device_id},
    {"status", status}
  };
  
  std::string request_data = request_body.dump();
  log_handler_->info("Updating OTA status to '{}' for device: {}", status, device_id);
  log_handler_->debug("Request data: {}", request_data);

  long http_code = 0;
  CURLcode res = performCurlRequest(api_url, "PUT", request_data, response, &http_code);

  if (res != CURLE_OK) {
    log_handler_->error("Failed to update OTA status: {}", curl_easy_strerror(res));
    return false;
  }

  if (http_code < 200 || http_code >= 300) {
    log_handler_->error("Server returned error code {} when updating OTA status: {}", http_code, response);
    return false;
  }

  try {
    // Parse the response to check for success
    if (!response.empty()) {
      auto json_response = nlohmann::json::parse(response);
      log_handler_->debug("OTA status update response: {}", json_response.dump());
      
      if (json_response.contains("status") && json_response["status"] == "success") {
        log_handler_->info("OTA status successfully updated to: {}", status);
        return true;
      }
    }
    
    // If response parsing fails or no explicit success, but HTTP status was success
    log_handler_->info("OTA status update completed (HTTP {})", http_code);
    return true;
  } catch (const std::exception &e) {
    log_handler_->error("Failed to parse OTA status update response: {}", e.what());
    // If parsing fails but HTTP status was success, still consider it successful
    if (http_code >= 200 && http_code < 300) {
      log_handler_->info("OTA status update completed despite parse error (HTTP {})", http_code);
      return true;
    }
    return false;
  }
}

std::string APIClient::getUserIdByHardwareSn(const std::string &hardware_sn) {
  if (hardware_sn.empty()) {
    log_handler_->error("Cannot get user ID: hardware_sn is empty");
    return "";
  }

  std::string api_url = base_url_ + "/api/devices/hardware/" + hardware_sn;
  std::string response;

  log_handler_->info("Getting user ID for hardware SN: {}", hardware_sn);
  log_handler_->debug("Request URL: {}", api_url);

  CURLcode res = performSimpleGetRequest(api_url, response);

  if (res != CURLE_OK) {
    log_handler_->error("Failed to get user ID by hardware SN: {}", curl_easy_strerror(res));
    return "";
  }

  if (response.empty()) {
    log_handler_->error("Empty response when getting user ID for hardware SN: {}", hardware_sn);
    return "";
  }

  try {
    auto json_response = nlohmann::json::parse(response);
    log_handler_->debug("User ID API response: {}", json_response.dump());
    
    // Check if the response contains user_id field
    if (!json_response.contains("user_id")) {
      log_handler_->error("Response does not contain 'user_id' field for hardware SN: {}", hardware_sn);
      return "";
    }
    
    if (!json_response["user_id"].is_string()) {
      log_handler_->error("'user_id' field is not a string for hardware SN: {}", hardware_sn);
      return "";
    }
    
    std::string user_id = json_response["user_id"].get<std::string>();
    
    // Validate the hardware_sn in response matches what we requested
    if (json_response.contains("hardware_sn") && json_response["hardware_sn"].is_string()) {
      std::string response_hardware_sn = json_response["hardware_sn"].get<std::string>();
      if (response_hardware_sn != hardware_sn) {
        log_handler_->warn("Response hardware_sn '{}' does not match requested '{}'", 
                          response_hardware_sn, hardware_sn);
      }
    }
    
    log_handler_->info("Successfully retrieved user ID '{}' for hardware SN: {}", user_id, hardware_sn);
    return user_id;
    
  } catch (const std::exception &e) {
    log_handler_->error("Failed to parse user ID response for hardware SN '{}': {}", hardware_sn, e.what());
    log_handler_->debug("Raw response: {}", response);
    return "";
  }
}

bool APIClient::reportSensorError(const std::string &sensor_type, 
                                 int error_code, 
                                 const std::string &error_message,
                                 const std::string &additional_info) {
  std::string device_id = get_device_id();
  
  if (device_id.empty()) {
    log_handler_->error("Cannot report sensor error: device_id is empty");
    return false;
  }

  // Validate sensor type
  std::vector<std::string> valid_sensors = {
    "camera", "weight_sensor", "temperature_humidity_sensor", 
    "microphone", "wifi", "bluetooth"
  };
  
  if (std::find(valid_sensors.begin(), valid_sensors.end(), sensor_type) == valid_sensors.end()) {
    log_handler_->error("Invalid sensor type: {}. Valid types are: camera, weight_sensor, temperature_humidity_sensor, microphone, wifi, bluetooth", sensor_type);
    return false;
  }

  std::string api_url = base_url_ + "/api/devices/" + device_id + "/sensor-errors";
  std::string response;

  // 限制字符串长度以符合数据库列限制
  std::string trimmed_error_message = error_message;
  std::string trimmed_additional_info = additional_info;
  
  if (trimmed_error_message.length() > 50) {
    trimmed_error_message = trimmed_error_message.substr(0, 47) + "...";
  }
  
  if (trimmed_additional_info.length() > 30) {
    trimmed_additional_info = trimmed_additional_info.substr(0, 27) + "...";
  }

  // Build request body as JSON according to README documentation
  nlohmann::json request_body = {
    {"device_id", device_id},
    {"sensor_type", sensor_type},
    {"report_time", std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now().time_since_epoch()).count()}
  };
  
  // Add optional fields if provided
  if (error_code > 0) {
    request_body["error_code"] = error_code;
  }
  
  if (!trimmed_error_message.empty()) {
    request_body["error_message"] = trimmed_error_message;
  }
  
  if (!trimmed_additional_info.empty()) {
    request_body["additional_info"] = trimmed_additional_info;
  }
  
  std::string request_data = request_body.dump();
  log_handler_->info("Reporting {} sensor error for device: {}", sensor_type, device_id);
  log_handler_->debug("Sensor error report data: {}", request_data);
  
  // 记录字符串裁剪信息
  if (error_message.length() > 50) {
    log_handler_->debug("Error message trimmed from {} to {} characters", error_message.length(), trimmed_error_message.length());
  }
  if (additional_info.length() > 30) {
    log_handler_->debug("Additional info trimmed from {} to {} characters", additional_info.length(), trimmed_additional_info.length());
  }

  long http_code = 0;
  CURLcode res = performCurlRequest(api_url, "POST", request_data, response, &http_code);

  if (res != CURLE_OK) {
    log_handler_->error("Failed to report sensor error: {}", curl_easy_strerror(res));
    return false;
  }

  if (http_code < 200 || http_code >= 300) {
    log_handler_->error("Server returned error code {} when reporting sensor error: {}", http_code, response);
    return false;
  }

  try {
    // Parse the response to check for success
    if (!response.empty()) {
      auto json_response = nlohmann::json::parse(response);
      log_handler_->debug("Sensor error report response: {}", json_response.dump());
      
      if (json_response.contains("status") && json_response["status"] == "success") {
        log_handler_->info("Sensor error reported successfully for {}", sensor_type);
        return true;
      }
    }
    
    // If response parsing fails or no explicit success, but HTTP status was success
    log_handler_->info("Sensor error report completed for {} (HTTP {})", sensor_type, http_code);
    return true;
  } catch (const std::exception &e) {
    log_handler_->error("Failed to parse sensor error report response: {}", e.what());
    // If parsing fails but HTTP status was success, still consider it successful
    if (http_code >= 200 && http_code < 300) {
      log_handler_->info("Sensor error report completed for {} despite parse error (HTTP {})", sensor_type, http_code);
      return true;
    }
    return false;
  }
}

bool APIClient::clearSensorError(const std::string &sensor_type) {
  std::string device_id = get_device_id();
  
  if (device_id.empty()) {
    log_handler_->error("Cannot clear sensor error: device_id is empty");
    return false;
  }

  // Validate sensor type
  std::vector<std::string> valid_sensors = {
    "camera", "weight_sensor", "temperature_humidity_sensor", 
    "microphone", "wifi", "bluetooth"
  };
  
  if (std::find(valid_sensors.begin(), valid_sensors.end(), sensor_type) == valid_sensors.end()) {
    log_handler_->error("Invalid sensor type: {}. Valid types are: camera, weight_sensor, temperature_humidity_sensor, microphone, wifi, bluetooth", sensor_type);
    return false;
  }

  // Build DELETE URL: /api/devices/{device_id}/sensor-errors/{sensor_type}
  std::string api_url = base_url_ + "/api/devices/" + device_id + "/sensor-errors/" + sensor_type;
  std::string response;

  log_handler_->info("Clearing {} sensor error status for device: {}", sensor_type, device_id);
  log_handler_->debug("Clear sensor error URL: {}", api_url);

  long http_code = 0;
  // Use DELETE method with empty request body
  CURLcode res = performCurlRequest(api_url, "DELETE", "", response, &http_code);

  if (res != CURLE_OK) {
    log_handler_->error("Failed to clear sensor error: {}", curl_easy_strerror(res));
    return false;
  }

  if (http_code < 200 || http_code >= 300) {
    log_handler_->error("Server returned error code {} when clearing sensor error: {}", http_code, response);
    return false;
  }

  try {
    // Parse the response to check for success
    if (!response.empty()) {
      auto json_response = nlohmann::json::parse(response);
      log_handler_->debug("Clear sensor error response: {}", json_response.dump());
      
      if (json_response.contains("status") && json_response["status"] == "success") {
        log_handler_->info("Sensor error status cleared successfully for {}", sensor_type);
        return true;
      }
    }
    
    // If response parsing fails or no explicit success, but HTTP status was success
    log_handler_->info("Sensor error clear completed for {} (HTTP {})", sensor_type, http_code);
    return true;
  } catch (const std::exception &e) {
    log_handler_->error("Failed to parse clear sensor error response: {}", e.what());
    // If parsing fails but HTTP status was success, still consider it successful
    if (http_code >= 200 && http_code < 300) {
      log_handler_->info("Sensor error clear completed for {} despite parse error (HTTP {})", sensor_type, http_code);
      return true;
    }
    return false;
  }
}

// 增强的网络请求方法，带重试和错误处理
CURLcode APIClient::performRequestWithRetry(const std::string &url, const std::string &method,
                                           const std::string &postData, std::string &response,
                                           long *http_code, int max_retries) {
  if (!isNetworkAvailable()) {
    log_handler_->warn("Network not available, skipping request to {}", url);
    return CURLE_COULDNT_CONNECT;
  }

  CURLcode last_result = CURLE_OK;
  int attempt = 0;

  while (attempt < max_retries) {
    attempt++;
    response.clear();  // 清空之前的响应

    CURL *curl = curl_easy_init();
    if (!curl) {
      log_handler_->error("Failed to initialize CURL handle for request (attempt {})", attempt);
      last_result = CURLE_FAILED_INIT;
      continue;
    }

    char error_buffer[CURL_ERROR_SIZE];
    error_buffer[0] = '\0';

    try {
      // 配置通用CURL选项
      configureCommonCurlOptions(curl, url, response, error_buffer);

      // 设置HTTP方法
      if (method == "POST") {
        curl_easy_setopt(curl, CURLOPT_POST, 1L);
        if (!postData.empty()) {
          curl_easy_setopt(curl, CURLOPT_POSTFIELDS, postData.c_str());
          curl_easy_setopt(curl, CURLOPT_POSTFIELDSIZE, postData.length());
        }
      } else if (method == "PUT") {
        curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "PUT");
        if (!postData.empty()) {
          curl_easy_setopt(curl, CURLOPT_POSTFIELDS, postData.c_str());
        }
      } else if (method == "DELETE") {
        curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "DELETE");
      }
      // GET是默认方法，不需要特殊设置

      // 设置JSON头部（如果有数据）
      struct curl_slist *headers = nullptr;
      if (!postData.empty()) {
        headers = curl_slist_append(headers, "Content-Type: application/json");
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
      }

      // 执行请求
      last_result = curl_easy_perform(curl);

      // 获取HTTP状态码
      if (http_code) {
        curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, http_code);
      }

      // 清理
      if (headers) {
        curl_slist_free_all(headers);
      }
      curl_easy_cleanup(curl);

      // 检查结果
      if (last_result == CURLE_OK) {
        log_handler_->debug("Request to {} succeeded on attempt {}", url, attempt);
        return CURLE_OK;
      } else {
        log_handler_->warn("Request to {} failed on attempt {}: {} ({})",
                          url, attempt, curl_easy_strerror(last_result), error_buffer);
      }

    } catch (const std::exception& e) {
      log_handler_->error("Exception during CURL request (attempt {}): {}", attempt, e.what());
      curl_easy_cleanup(curl);
      last_result = CURLE_FAILED_INIT;
    }

    // 如果不是最后一次尝试，等待后重试
    if (attempt < max_retries) {
      int delay_ms = RETRY_DELAY_MS * attempt;  // 递增延迟
      log_handler_->debug("Retrying request to {} in {}ms (attempt {}/{})",
                         url, delay_ms, attempt + 1, max_retries);
      std::this_thread::sleep_for(std::chrono::milliseconds(delay_ms));
    }
  }

  log_handler_->error("Request to {} failed after {} attempts, last error: {}",
                     url, max_retries, curl_easy_strerror(last_result));
  return last_result;
}

// 检查网络可用性
bool APIClient::isNetworkAvailable() const {
  // 检查WiFi模块状态
  try {
    auto& wifi = Wifi::getInstance();
    return wifi.isConnected() && wifi.isNetworkStable();
  } catch (const std::exception& e) {
    log_handler_->debug("Failed to check WiFi status: {}", e.what());
    return false;
  }
}

// 配置通用CURL选项
void APIClient::configureCommonCurlOptions(CURL *curl, const std::string &url,
                                          std::string &response, char *error_buffer) {
  curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
  curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
  curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
  curl_easy_setopt(curl, CURLOPT_ERRORBUFFER, error_buffer);
  curl_easy_setopt(curl, CURLOPT_FAILONERROR, 1L);
  curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);
  curl_easy_setopt(curl, CURLOPT_NOSIGNAL, 1L);
  curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);  // 在某些环境中可能需要
  curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);

  // 设置超时时间
  curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);        // 总超时时间
  curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 5L);  // 连接超时时间

  // 设置用户代理
  curl_easy_setopt(curl, CURLOPT_USERAGENT, "ABY-Box/1.0");
}

} // namespace aby_box
