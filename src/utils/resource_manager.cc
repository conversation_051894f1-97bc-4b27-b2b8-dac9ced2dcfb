// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "utils/resource_manager.hpp"
#include <fstream>
#include <sstream>
#include <thread>
#include <filesystem>
#include <dirent.h>
#include <unistd.h>
#include <sys/sysinfo.h>

namespace aby_box {

// FileHandle implementation
FileHandle::FileHandle(const std::string& path, std::ios::openmode mode) 
  : file_(std::make_unique<std::fstream>(path, mode)) {
}

FileHandle::~FileHandle() {
  close();
}

FileHandle::FileHandle(FileHandle&& other) noexcept 
  : file_(std::move(other.file_)) {
}

FileHandle& FileHandle::operator=(FileHandle&& other) noexcept {
  if (this != &other) {
    close();
    file_ = std::move(other.file_);
  }
  return *this;
}

bool FileHandle::is_open() const {
  return file_ && file_->is_open();
}

std::fstream& FileHandle::get() {
  if (!file_) {
    throw std::runtime_error("FileHandle is not initialized");
  }
  return *file_;
}

void FileHandle::close() {
  if (file_ && file_->is_open()) {
    file_->close();
  }
}

// ResourceManager implementation
ResourceManager::ResourceManager() : Loggable("ResourceManager") {
  log_handler_->info("ResourceManager initialized");
}

ResourceManager::~ResourceManager() {
  stopMemoryMonitoring();
  stopFdMonitoring();
  cleanupAll();
  log_handler_->info("ResourceManager destroyed");
}

ResourceManager& ResourceManager::getInstance() {
  static ResourceManager instance;
  return instance;
}

void ResourceManager::registerCleanup(const std::string& name, std::function<void()> cleanup_func) {
  std::lock_guard<std::mutex> lock(cleanup_mutex_);
  cleanup_functions_[name] = cleanup_func;
  log_handler_->debug("Registered cleanup function: {}", name);
}

void ResourceManager::unregisterCleanup(const std::string& name) {
  std::lock_guard<std::mutex> lock(cleanup_mutex_);
  auto it = cleanup_functions_.find(name);
  if (it != cleanup_functions_.end()) {
    cleanup_functions_.erase(it);
    log_handler_->debug("Unregistered cleanup function: {}", name);
  }
}

void ResourceManager::cleanupAll() {
  std::lock_guard<std::mutex> lock(cleanup_mutex_);
  log_handler_->info("Executing {} cleanup functions", cleanup_functions_.size());
  
  for (const auto& [name, cleanup_func] : cleanup_functions_) {
    try {
      log_handler_->debug("Executing cleanup: {}", name);
      cleanup_func();
    } catch (const std::exception& e) {
      log_handler_->error("Cleanup function '{}' failed: {}", name, e.what());
    } catch (...) {
      log_handler_->error("Cleanup function '{}' failed with unknown exception", name);
    }
  }
  
  cleanup_functions_.clear();
  log_handler_->info("All cleanup functions executed");
}

void ResourceManager::startMemoryMonitoring(std::chrono::milliseconds interval) {
  if (memory_monitoring_active_.exchange(true)) {
    log_handler_->warn("Memory monitoring already active");
    return;
  }
  
  memory_monitor_thread_ = std::make_unique<std::thread>([this, interval]() {
    memoryMonitorLoop();
  });
  
  log_handler_->info("Memory monitoring started with interval: {}ms", interval.count());
}

void ResourceManager::stopMemoryMonitoring() {
  if (!memory_monitoring_active_.exchange(false)) {
    return;
  }
  
  if (memory_monitor_thread_ && memory_monitor_thread_->joinable()) {
    memory_monitor_thread_->join();
  }
  memory_monitor_thread_.reset();
  
  log_handler_->info("Memory monitoring stopped");
}

void ResourceManager::startFdMonitoring(std::chrono::milliseconds interval) {
  if (fd_monitoring_active_.exchange(true)) {
    log_handler_->warn("FD monitoring already active");
    return;
  }
  
  fd_monitor_thread_ = std::make_unique<std::thread>([this, interval]() {
    fdMonitorLoop();
  });
  
  log_handler_->info("File descriptor monitoring started with interval: {}ms", interval.count());
}

void ResourceManager::stopFdMonitoring() {
  if (!fd_monitoring_active_.exchange(false)) {
    return;
  }
  
  if (fd_monitor_thread_ && fd_monitor_thread_->joinable()) {
    fd_monitor_thread_->join();
  }
  fd_monitor_thread_.reset();
  
  log_handler_->info("File descriptor monitoring stopped");
}

ResourceManager::MemoryStats ResourceManager::getMemoryStats() const {
  MemoryStats stats;
  
  // Get system memory info
  struct sysinfo si;
  if (sysinfo(&si) == 0) {
    stats.total_memory_kb = si.totalram * si.mem_unit / 1024;
    stats.available_memory_kb = si.freeram * si.mem_unit / 1024;
    stats.used_memory_kb = stats.total_memory_kb - stats.available_memory_kb;
    stats.memory_usage_percent = (double)stats.used_memory_kb / stats.total_memory_kb * 100.0;
  }
  
  // Get process memory info
  std::ifstream status_file("/proc/self/status");
  std::string line;
  while (std::getline(status_file, line)) {
    if (line.find("VmRSS:") == 0) {
      std::istringstream iss(line);
      std::string key, value, unit;
      iss >> key >> value >> unit;
      stats.process_memory_kb = std::stoul(value);
      break;
    }
  }
  
  return stats;
}

int ResourceManager::getCurrentFdCount() const {
  int fd_count = 0;
  DIR* dir = opendir("/proc/self/fd");
  if (dir) {
    struct dirent* entry;
    while ((entry = readdir(dir)) != nullptr) {
      if (entry->d_name[0] != '.') {
        fd_count++;
      }
    }
    closedir(dir);
  }
  return fd_count;
}

void ResourceManager::emergencyCleanup() {
  log_handler_->error("Emergency cleanup triggered!");
  
  // Force garbage collection
  cleanupAll();
  
  // Try to free system caches
  system("sync");
  system("echo 1 > /proc/sys/vm/drop_caches 2>/dev/null || true");
  
  log_handler_->info("Emergency cleanup completed");
}

void ResourceManager::memoryMonitorLoop() {
  while (memory_monitoring_active_) {
    try {
      auto stats = getMemoryStats();
      
      if (stats.memory_usage_percent > MEMORY_CRITICAL_THRESHOLD) {
        log_handler_->error("Critical memory usage: {:.1f}% ({} KB used of {} KB)", 
                           stats.memory_usage_percent, stats.used_memory_kb, stats.total_memory_kb);
        handleMemoryPressure();
      } else if (stats.memory_usage_percent > MEMORY_WARNING_THRESHOLD) {
        log_handler_->warn("High memory usage: {:.1f}% ({} KB used of {} KB)", 
                          stats.memory_usage_percent, stats.used_memory_kb, stats.total_memory_kb);
      }
      
      log_handler_->debug("Memory usage: {:.1f}%, Process: {} KB", 
                         stats.memory_usage_percent, stats.process_memory_kb);
      
    } catch (const std::exception& e) {
      log_handler_->error("Memory monitoring error: {}", e.what());
    }
    
    std::this_thread::sleep_for(std::chrono::seconds(30));
  }
}

void ResourceManager::fdMonitorLoop() {
  while (fd_monitoring_active_) {
    try {
      int fd_count = getCurrentFdCount();
      
      if (fd_count > FD_WARNING_THRESHOLD) {
        log_handler_->warn("High file descriptor usage: {} open FDs", fd_count);
      }
      
      log_handler_->debug("File descriptors in use: {}", fd_count);
      
    } catch (const std::exception& e) {
      log_handler_->error("FD monitoring error: {}", e.what());
    }
    
    std::this_thread::sleep_for(std::chrono::seconds(60));
  }
}

void ResourceManager::handleMemoryPressure() {
  log_handler_->info("Handling memory pressure...");
  
  // Trigger cleanup functions
  cleanupAll();
  
  // Force system cache cleanup
  system("sync");
  system("echo 1 > /proc/sys/vm/drop_caches 2>/dev/null || true");
  
  log_handler_->info("Memory pressure handling completed");
}

} // namespace aby_box
