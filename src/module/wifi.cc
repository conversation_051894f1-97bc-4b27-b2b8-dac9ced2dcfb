// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "module/wifi.hpp"

namespace aby_box {

// 公共WiFi连接接口实现
bool Wifi::connectWifi(const std::string &ssid, const std::string &password) {
  std::lock_guard<std::mutex> lock(wifi_mutex_);
  
  if (ssid.empty() || password.empty()) {
    log_handler_->error("WiFi SSID or password cannot be empty");
    return false;
  }
  
  log_handler_->info("Connecting to WiFi network: {}", ssid);
  
  // 先断开现有连接
  stop_wpa_supplicant();
  std::this_thread::sleep_for(std::chrono::seconds(2));
  
  // 保存当前连接信息
  current_ssid_ = ssid;
  current_password_ = password;
  
  // 尝试连接
  bool success = connect(ssid, password);
  if (success) {
    log_handler_->info("Successfully connected to WiFi network: {}", ssid);
  } else {
    log_handler_->error("Failed to connect to WiFi network: {}", ssid);
  }
  
  return success;
}

bool Wifi::disconnectWifi() {
  std::lock_guard<std::mutex> lock(wifi_mutex_);
  
  log_handler_->info("Disconnecting from WiFi");
  
  stop_wpa_supplicant();
  
  // 清除保存的连接信息
  current_ssid_.clear();
  current_password_.clear();
  last_connected_state_ = false;
  
  log_handler_->info("WiFi disconnected");
  return true;
}

bool Wifi::reconnectWifi() {
  std::lock_guard<std::mutex> lock(wifi_mutex_);
  
  if (current_ssid_.empty() || current_password_.empty()) {
    log_handler_->warn("No saved WiFi credentials available for reconnection");
    return false;
  }
  
  log_handler_->info("Reconnecting to WiFi network: {}", current_ssid_);
  
  // 先断开现有连接
  stop_wpa_supplicant();
  std::this_thread::sleep_for(std::chrono::seconds(2));
  
  // 重新连接
  bool success = connect(current_ssid_, current_password_);
  if (success) {
    log_handler_->info("Successfully reconnected to WiFi network: {}", current_ssid_);
  } else {
    log_handler_->error("Failed to reconnect to WiFi network: {}", current_ssid_);
  }
  
  return success;
}

bool Wifi::isConnected() {
  return check_network_connectivity();
}

bool Wifi::connect(const std::string &ssid, const std::string &password) {
  // Create wpa_supplicant configuration file
  std::ofstream wpa_conf("/etc/wpa_supplicant.conf");
  if (!wpa_conf.is_open()) {
    log_handler_->error("Failed to open /etc/wpa_supplicant.conf for writing");
    return false;
  }

  wpa_conf << "ctrl_interface=DIR=/var/run/wpa_supplicant\n";
  wpa_conf << "update_config=1\n";
  wpa_conf << "ap_scan=1\n\n";
  wpa_conf << "network={\n";
  wpa_conf << "    ssid=\"" << ssid << "\"\n";
  wpa_conf << "    psk=\"" << password << "\"\n";
  wpa_conf << "    key_mgmt=WPA-PSK\n";
  wpa_conf << "}\n";

  wpa_conf.close();
  sync();

  return connect_wpa_supplicant();
}

bool Wifi::connect_wpa_supplicant() {
  // Execute wpa_supplicant command
  std::string command =
      "wpa_supplicant -Dnl80211 -iwlan0 -c/etc/wpa_supplicant.conf -B";
  int result = std::system(command.c_str());
  if (result != 0) {
    log_handler_->error("Failed to execute wpa_supplicant command");
    return false;
  }

  // Wait a moment for wpa_supplicant to start
  std::this_thread::sleep_for(std::chrono::seconds(3));

  // Execute dhclient command to obtain IP address
  result = std::system("udhcpc -b -i wlan0 -R");
  if (result != 0) {
    log_handler_->error("Failed to execute dhclient command");
    return false;
  }
  
  // Wait for IP assignment
  std::this_thread::sleep_for(std::chrono::seconds(2));
  
  return true;
}

void Wifi::stop_wpa_supplicant() {
  int result = std::system("killall wpa_supplicant");
  if (result != 0) {
    log_handler_->debug("No wpa_supplicant processes to stop");
  } else {
    log_handler_->info("Successfully stopped wpa_supplicant processes");
  }

  result = std::system("killall udhcpc");
  if (result != 0) {
    log_handler_->debug("No udhcpc processes to stop");
  } else {
    log_handler_->info("Successfully stopped udhcpc processes");
  }
}

bool Wifi::check_network_connectivity() {
  // 首先检查网络接口状态
  std::string interface_cmd = "ip link show wlan0 | grep 'state UP' > /dev/null 2>&1";
  if (system(interface_cmd.c_str()) != 0) {
    log_handler_->debug("WiFi interface wlan0 is not UP");
    return false;
  }

  // 检查是否有IP地址
  std::string ip_cmd = "ip addr show wlan0 | grep 'inet ' > /dev/null 2>&1";
  if (system(ip_cmd.c_str()) != 0) {
    log_handler_->debug("WiFi interface wlan0 has no IP address");
    return false;
  }

  // 使用多个DNS服务器进行连通性测试，提高可靠性
  std::vector<std::string> dns_servers = {"*******", "*******", "***************"};
  int successful_pings = 0;

  for (const auto& dns : dns_servers) {
    std::string cmd = "ping -c 1 -W 2 " + dns + " > /dev/null 2>&1";
    if (system(cmd.c_str()) == 0) {
      successful_pings++;
      // 如果至少有一个DNS服务器可达，就认为网络连通
      if (successful_pings >= 1) {
        // 更新信号强度
        updateSignalStrength();
        return true;
      }
    }
  }

  log_handler_->debug("Network connectivity check failed for all DNS servers");
  return false;
}

void Wifi::network_check_loop() {
  log_handler_->info("Network check loop started");

  while (is_running_) {
    try {
      bool is_connected = check_network_connectivity();

      // 处理网络连接状态变化
      if (is_connected) {
        if (!last_connected_state_) {
          log_handler_->info("Network connectivity restored");
          last_successful_connection_ = std::chrono::steady_clock::now();
          consecutive_failures_ = 0;

          // 等待网络稳定
          if (waitForNetworkStability(10)) {
            network_stable_ = true;
            log_handler_->info("Network is stable and ready");

            // 发送心跳包确认网络可用性
            try {
              auto response = APIClient::getInstance().sendHeartbeat();
              log_handler_->info("Network connectivity confirmed via heartbeat");
              reset_error_state();  // 重置模块错误状态
            } catch (const std::exception& e) {
              log_handler_->warn("Heartbeat failed despite network connectivity: {}", e.what());
            }
          }
        }
      } else {
        if (last_connected_state_) {
          log_handler_->warn("Network connectivity lost");
          network_stable_ = false;
          consecutive_failures_++;
          probe_error();  // 记录错误状态
        }

        // 如果有保存的WiFi凭据且连续失败次数未超过阈值，尝试重连
        if (!current_ssid_.empty() && consecutive_failures_ < MAX_CONSECUTIVE_FAILURES) {
          log_handler_->info("Attempting to reconnect (failure count: {})", consecutive_failures_.load());

          if (performReconnectWithBackoff()) {
            log_handler_->info("Reconnection attempt initiated");
          } else {
            log_handler_->error("Reconnection attempt failed");
          }
        } else if (consecutive_failures_ >= MAX_CONSECUTIVE_FAILURES) {
          log_handler_->error("Maximum consecutive failures reached ({}), stopping automatic reconnection",
                             MAX_CONSECUTIVE_FAILURES);
        }
      }

      last_connected_state_ = is_connected;

      // 通知等待网络状态的线程
      {
        std::lock_guard<std::mutex> lock(wifi_mutex_);
        network_cv_.notify_all();
      }

    } catch (const std::exception& e) {
      log_handler_->error("Exception in network check loop: {}", e.what());
      probe_error();
    }

    // 使用可中断的睡眠
    std::unique_lock<std::mutex> lock(wifi_mutex_);
    network_cv_.wait_for(lock, std::chrono::milliseconds(NETWORK_CHECK_INTERVAL_MS),
                        [this] { return !is_running_; });
  }

  log_handler_->info("Network check loop stopped");
}

bool Wifi::init() {
  log_handler_->info("WiFi module initialized");
  return true;
}

bool Wifi::start() {
  if (is_running_) {
    log_handler_->warn("WiFi module is already running");
    return true;
  }
  
  is_running_ = true;
  
  // 启动网络检查线程
  network_check_thread_ =
      std::make_unique<std::thread>(&Wifi::network_check_loop, this);
  
  // 设置线程名称
  pthread_setname_np(network_check_thread_->native_handle(),
                     "aby_wifi_network_check_loop");
  
  log_handler_->info("WiFi module started");
  return true;
}

bool Wifi::stop() {
  if (!is_running_) {
    return true;
  }

  log_handler_->info("Stopping WiFi module");
  is_running_ = false;

  // 通知网络检查线程退出
  {
    std::lock_guard<std::mutex> lock(wifi_mutex_);
    network_cv_.notify_all();
  }

  return true;
}

void Wifi::join() {
  if (network_check_thread_ && network_check_thread_->joinable()) {
    network_check_thread_->join();
  }
  log_handler_->info("WiFi module stopped");
}

// 新增的网络状态查询方法
bool Wifi::isNetworkStable() const {
  return network_stable_.load();
}

int Wifi::getSignalStrength() const {
  return signal_strength_.load();
}

std::string Wifi::getConnectionStatus() const {
  std::lock_guard<std::mutex> lock(wifi_mutex_);

  if (!last_connected_state_) {
    return "disconnected";
  }

  if (network_stable_) {
    return "stable";
  }

  return "connecting";
}

// 增强的重连方法，使用指数退避策略
bool Wifi::performReconnectWithBackoff() {
  std::lock_guard<std::mutex> lock(wifi_mutex_);

  auto now = std::chrono::steady_clock::now();
  auto time_since_last_attempt = std::chrono::duration_cast<std::chrono::milliseconds>(
      now - last_reconnect_attempt_).count();

  // 计算退避延迟（指数退避，但有最大限制）
  int delay_ms = std::min(
      RECONNECT_BASE_DELAY_MS * (1 << std::min(consecutive_failures_.load(), 5)),
      RECONNECT_MAX_DELAY_MS
  );

  if (time_since_last_attempt < delay_ms) {
    log_handler_->debug("Reconnect attempt too soon, waiting {} more ms",
                       delay_ms - time_since_last_attempt);
    return false;
  }

  last_reconnect_attempt_ = now;

  log_handler_->info("Performing reconnect with backoff (attempt {}, delay {}ms)",
                    consecutive_failures_.load() + 1, delay_ms);

  // 先停止现有连接
  stop_wpa_supplicant();
  std::this_thread::sleep_for(std::chrono::seconds(2));

  // 尝试重新连接
  return connect_wpa_supplicant();
}

// 等待网络稳定
bool Wifi::waitForNetworkStability(int timeout_seconds) {
  auto start_time = std::chrono::steady_clock::now();
  auto timeout_duration = std::chrono::seconds(timeout_seconds);

  log_handler_->debug("Waiting for network stability (timeout: {}s)", timeout_seconds);

  while (std::chrono::steady_clock::now() - start_time < timeout_duration) {
    if (!is_running_) {
      return false;
    }

    // 连续检查网络连通性
    bool stable = true;
    for (int i = 0; i < 3 && stable; ++i) {
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      if (!check_network_connectivity()) {
        stable = false;
      }
    }

    if (stable) {
      log_handler_->debug("Network stability confirmed");
      return true;
    }

    std::this_thread::sleep_for(std::chrono::milliseconds(2000));
  }

  log_handler_->warn("Network stability timeout after {}s", timeout_seconds);
  return false;
}

// 重置连接状态
void Wifi::resetConnectionState() {
  std::lock_guard<std::mutex> lock(wifi_mutex_);

  consecutive_failures_ = 0;
  network_stable_ = false;
  last_connected_state_ = false;
  signal_strength_ = 0;

  log_handler_->info("WiFi connection state reset");
}

// 验证网络配置
bool Wifi::validateNetworkConfiguration() const {
  std::lock_guard<std::mutex> lock(wifi_mutex_);

  if (current_ssid_.empty()) {
    log_handler_->debug("No SSID configured");
    return false;
  }

  if (current_password_.empty()) {
    log_handler_->debug("No password configured");
    return false;
  }

  // 检查wpa_supplicant配置文件
  std::ifstream wpa_conf("/etc/wpa_supplicant.conf");
  if (!wpa_conf.is_open()) {
    log_handler_->debug("wpa_supplicant.conf not found");
    return false;
  }

  std::string line;
  bool ssid_found = false;
  while (std::getline(wpa_conf, line)) {
    if (line.find("ssid=\"" + current_ssid_ + "\"") != std::string::npos) {
      ssid_found = true;
      break;
    }
  }

  if (!ssid_found) {
    log_handler_->debug("SSID not found in wpa_supplicant.conf");
    return false;
  }

  return true;
}

// 更新信号强度
void Wifi::updateSignalStrength() {
  std::string signal_cmd = "iw dev wlan0 link | grep signal | awk '{print $2}'";
  FILE* signal_file = popen(signal_cmd.c_str(), "r");
  if (signal_file) {
    char buffer[128];
    if (fgets(buffer, sizeof(buffer), signal_file) != NULL) {
      int strength = std::atoi(buffer);
      signal_strength_ = strength;
      log_handler_->debug("WiFi signal strength: {} dBm", strength);
    }
    pclose(signal_file);
  }
}

} // namespace aby_box