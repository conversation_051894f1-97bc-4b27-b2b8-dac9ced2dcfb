// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "module/wifi.hpp"

namespace aby_box {

// 公共WiFi连接接口实现
bool Wifi::connectWifi(const std::string &ssid, const std::string &password) {
  std::lock_guard<std::mutex> lock(wifi_mutex_);
  
  if (ssid.empty() || password.empty()) {
    log_handler_->error("WiFi SSID or password cannot be empty");
    return false;
  }
  
  log_handler_->info("Connecting to WiFi network: {}", ssid);
  
  // 先断开现有连接
  stop_wpa_supplicant();
  std::this_thread::sleep_for(std::chrono::seconds(2));
  
  // 保存当前连接信息
  current_ssid_ = ssid;
  current_password_ = password;
  
  // 尝试连接
  bool success = connect(ssid, password);
  if (success) {
    log_handler_->info("Successfully connected to WiFi network: {}", ssid);
  } else {
    log_handler_->error("Failed to connect to WiFi network: {}", ssid);
  }
  
  return success;
}

bool Wifi::disconnectWifi() {
  std::lock_guard<std::mutex> lock(wifi_mutex_);
  
  log_handler_->info("Disconnecting from WiFi");
  
  stop_wpa_supplicant();
  
  // 清除保存的连接信息
  current_ssid_.clear();
  current_password_.clear();
  last_connected_state_ = false;
  
  log_handler_->info("WiFi disconnected");
  return true;
}

bool Wifi::reconnectWifi() {
  std::lock_guard<std::mutex> lock(wifi_mutex_);
  
  if (current_ssid_.empty() || current_password_.empty()) {
    log_handler_->warn("No saved WiFi credentials available for reconnection");
    return false;
  }
  
  log_handler_->info("Reconnecting to WiFi network: {}", current_ssid_);
  
  // 先断开现有连接
  stop_wpa_supplicant();
  std::this_thread::sleep_for(std::chrono::seconds(2));
  
  // 重新连接
  bool success = connect(current_ssid_, current_password_);
  if (success) {
    log_handler_->info("Successfully reconnected to WiFi network: {}", current_ssid_);
  } else {
    log_handler_->error("Failed to reconnect to WiFi network: {}", current_ssid_);
  }
  
  return success;
}

bool Wifi::isConnected() {
  return check_network_connectivity();
}

bool Wifi::connect(const std::string &ssid, const std::string &password) {
  // Create wpa_supplicant configuration file
  std::ofstream wpa_conf("/etc/wpa_supplicant.conf");
  if (!wpa_conf.is_open()) {
    log_handler_->error("Failed to open /etc/wpa_supplicant.conf for writing");
    return false;
  }

  wpa_conf << "ctrl_interface=DIR=/var/run/wpa_supplicant\n";
  wpa_conf << "update_config=1\n";
  wpa_conf << "ap_scan=1\n\n";
  wpa_conf << "network={\n";
  wpa_conf << "    ssid=\"" << ssid << "\"\n";
  wpa_conf << "    psk=\"" << password << "\"\n";
  wpa_conf << "    key_mgmt=WPA-PSK\n";
  wpa_conf << "}\n";

  wpa_conf.close();
  sync();

  return connect_wpa_supplicant();
}

bool Wifi::connect_wpa_supplicant() {
  // Execute wpa_supplicant command
  std::string command =
      "wpa_supplicant -Dnl80211 -iwlan0 -c/etc/wpa_supplicant.conf -B";
  int result = std::system(command.c_str());
  if (result != 0) {
    log_handler_->error("Failed to execute wpa_supplicant command");
    return false;
  }

  // Wait a moment for wpa_supplicant to start
  std::this_thread::sleep_for(std::chrono::seconds(3));

  // Execute dhclient command to obtain IP address
  result = std::system("udhcpc -b -i wlan0 -R");
  if (result != 0) {
    log_handler_->error("Failed to execute dhclient command");
    return false;
  }
  
  // Wait for IP assignment
  std::this_thread::sleep_for(std::chrono::seconds(2));
  
  return true;
}

void Wifi::stop_wpa_supplicant() {
  int result = std::system("killall wpa_supplicant");
  if (result != 0) {
    log_handler_->debug("No wpa_supplicant processes to stop");
  } else {
    log_handler_->info("Successfully stopped wpa_supplicant processes");
  }

  result = std::system("killall udhcpc");
  if (result != 0) {
    log_handler_->debug("No udhcpc processes to stop");
  } else {
    log_handler_->info("Successfully stopped udhcpc processes");
  }
}

bool Wifi::check_network_connectivity() {
  std::string cmd = "ping -c 3 -W 2 ******* > /dev/null 2>&1";
  bool connected = (system(cmd.c_str()) == 0);
  return connected;
}

void Wifi::network_check_loop() {
  while (is_running_) {
    bool is_connected = check_network_connectivity();
    
    if (!is_connected && !current_ssid_.empty()) {
      log_handler_->info("Network connectivity lost, attempting to reconnect...");
      // 不需要锁，因为connect_wpa_supplicant不会修改current_ssid_等变量
      connect_wpa_supplicant();
    }
    
    if (is_connected && !last_connected_state_) {
      auto response = APIClient::getInstance().sendHeartbeat();
      log_handler_->info("Network is connected to internet");
    } else if (!is_connected && last_connected_state_) {
      log_handler_->warn("Network is disconnected from internet");
    }
    
    last_connected_state_ = is_connected;
    std::this_thread::sleep_for(std::chrono::seconds(300));
  }
}

bool Wifi::init() {
  log_handler_->info("WiFi module initialized");
  return true;
}

bool Wifi::start() {
  if (is_running_) {
    log_handler_->warn("WiFi module is already running");
    return true;
  }
  
  is_running_ = true;
  
  // 启动网络检查线程
  network_check_thread_ =
      std::make_unique<std::thread>(&Wifi::network_check_loop, this);
  
  // 设置线程名称
  pthread_setname_np(network_check_thread_->native_handle(),
                     "aby_wifi_network_check_loop");
  
  log_handler_->info("WiFi module started");
  return true;
}

bool Wifi::stop() {
  if (!is_running_) {
    return true;
  }
  
  log_handler_->info("Stopping WiFi module");
  is_running_ = false;
  return true;
}

void Wifi::join() {
  if (network_check_thread_ && network_check_thread_->joinable()) {
    network_check_thread_->join();
  }
  log_handler_->info("WiFi module stopped");
}

} // namespace aby_box