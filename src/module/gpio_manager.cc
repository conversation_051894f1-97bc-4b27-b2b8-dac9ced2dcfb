#include "module/gpio_manager.hpp"
#include <fcntl.h>
#include <fstream>
#include <sys/stat.h>
#include <unistd.h>
#include <mutex>

namespace aby_box {

GpioManager::GpioManager(const std::string &module_name)
    : BaseModule(module_name) {}

GpioManager::~GpioManager() {
    stop();
    join();
}

bool GpioManager::init() {
    log_handler_->info("GPIO Manager initialized");
    return true;
}

bool GpioManager::start() {
    if (is_running_) {
        return true;
    }
    
    is_running_ = true;
    monitor_thread_ = std::make_unique<std::thread>(&GpioManager::monitor_loop, this);
    pthread_setname_np(monitor_thread_->native_handle(), "aby_gpio_monitor");
    
    log_handler_->info("GPIO Manager started");
    return true;
}

bool GpioManager::stop() {
    is_running_ = false;
    
    // 清理所有GPIO
    std::lock_guard<std::mutex> lock(gpio_map_mutex_);
    for (auto& pair : gpio_map_) {
        cleanup_single_gpio(pair.first);
    }
    gpio_map_.clear();
    
    log_handler_->info("GPIO Manager stopped");
    return true;
}

void GpioManager::join() {
    if (monitor_thread_ && monitor_thread_->joinable()) {
        monitor_thread_->join();
    }
}

bool GpioManager::configure_gpio(const GpioConfig& config) {
    std::lock_guard<std::mutex> lock(gpio_map_mutex_);
    
    // 检查GPIO是否已经配置
    if (gpio_map_.find(config.pin) != gpio_map_.end()) {
        log_handler_->warn("GPIO {} already configured, removing old configuration", config.pin);
        cleanup_single_gpio(config.pin);
        gpio_map_.erase(config.pin);
    }
    
    // 初始化GPIO
    if (!init_single_gpio(config)) {
        log_handler_->error("Failed to initialize GPIO {}", config.pin);
        return false;
    }
    
    // 创建GPIO信息对象
    auto gpio_info = std::make_unique<GpioInfo>(config);
    
    // 读取初始值
    int initial_value = read_gpio(config.pin);
    if (initial_value != -1) {
        gpio_info->last_value = initial_value;
    }
    
    gpio_map_[config.pin] = std::move(gpio_info);
    
    log_handler_->info("GPIO {} configured successfully (direction: {}, debounce: {}ms)", 
                      config.pin, 
                      config.direction == GpioDirection::INPUT ? "input" : "output",
                      config.debounce_time_ms);
    
    return true;
}

bool GpioManager::remove_gpio(int pin) {
    std::lock_guard<std::mutex> lock(gpio_map_mutex_);
    
    auto it = gpio_map_.find(pin);
    if (it == gpio_map_.end()) {
        log_handler_->warn("GPIO {} not found", pin);
        return false;
    }
    
    cleanup_single_gpio(pin);
    gpio_map_.erase(it);
    
    log_handler_->info("GPIO {} removed", pin);
    return true;
}

int GpioManager::read_gpio(int pin) {
    std::string value_path = get_gpio_path(pin, "value");
    std::ifstream value_file(value_path);
    if (!value_file.is_open()) {
        log_handler_->error("Cannot read GPIO {}: {}", pin, value_path);
        return -1;
    }
    
    int value;
    value_file >> value;
    value_file.close();
    
    return value;
}

bool GpioManager::write_gpio(int pin, int value) {
    std::lock_guard<std::mutex> lock(gpio_map_mutex_);
    
    auto it = gpio_map_.find(pin);
    if (it == gpio_map_.end()) {
        log_handler_->error("GPIO {} not configured", pin);
        return false;
    }
    
    if (it->second->config.direction != GpioDirection::OUTPUT) {
        log_handler_->error("GPIO {} is not configured as output", pin);
        return false;
    }
    
    std::string value_path = get_gpio_path(pin, "value");
    std::ofstream value_file(value_path);
    if (!value_file.is_open()) {
        log_handler_->error("Cannot write to GPIO {}: {}", pin, value_path);
        return false;
    }
    
    value_file << value;
    value_file.close();
    
    // 更新缓存值
    it->second->last_value = value;
    
    return true;
}

bool GpioManager::register_callback(int pin, GpioCallback callback) {
    std::lock_guard<std::mutex> lock(gpio_map_mutex_);
    
    auto it = gpio_map_.find(pin);
    if (it == gpio_map_.end()) {
        log_handler_->error("GPIO {} not configured", pin);
        return false;
    }
    
    it->second->callback = callback;
    it->second->is_monitoring = true;
    
    log_handler_->info("Callback registered for GPIO {}", pin);
    return true;
}

bool GpioManager::unregister_callback(int pin) {
    std::lock_guard<std::mutex> lock(gpio_map_mutex_);
    
    auto it = gpio_map_.find(pin);
    if (it == gpio_map_.end()) {
        log_handler_->error("GPIO {} not configured", pin);
        return false;
    }
    
    it->second->callback = nullptr;
    it->second->is_monitoring = false;
    
    log_handler_->info("Callback unregistered for GPIO {}", pin);
    return true;
}

bool GpioManager::configure_multiple_gpios(const std::vector<GpioConfig>& configs) {
    bool all_success = true;
    for (const auto& config : configs) {
        if (!configure_gpio(config)) {
            all_success = false;
        }
    }
    return all_success;
}

std::vector<int> GpioManager::read_multiple_gpios(const std::vector<int>& pins) {
    std::vector<int> values;
    values.reserve(pins.size());
    
    for (int pin : pins) {
        values.push_back(read_gpio(pin));
    }
    
    return values;
}

void GpioManager::monitor_loop() {
    log_handler_->info("GPIO monitor loop started");
    
    while (is_running_) {
        std::lock_guard<std::mutex> lock(gpio_map_mutex_);
        
        for (auto& pair : gpio_map_) {
            auto& gpio_info = *pair.second;
            
            // 只监控输入GPIO且注册了回调的
            if (gpio_info.config.direction != GpioDirection::INPUT || 
                !gpio_info.is_monitoring || !gpio_info.callback) {
                continue;
            }
            
            int current_value = read_gpio(gpio_info.config.pin);
            if (current_value == -1) {
                continue;  // 读取失败，跳过
            }
            
            int last_value = gpio_info.last_value.load();
            
            // 检查是否有状态变化
            bool value_changed = (current_value != last_value);
            bool should_trigger = false;
            
            if (value_changed) {
                switch (gpio_info.config.edge) {
                    case GpioEdge::RISING:
                        should_trigger = (last_value == 0 && current_value == 1);
                        break;
                    case GpioEdge::FALLING:
                        should_trigger = (last_value == 1 && current_value == 0);
                        break;
                    case GpioEdge::BOTH:
                        should_trigger = true;
                        break;
                    case GpioEdge::NONE:
                    default:
                        should_trigger = false;
                        break;
                }
                
                // 检查防抖
                if (should_trigger && is_debounce_elapsed(gpio_info)) {
                    uint64_t timestamp = std::chrono::duration_cast<std::chrono::microseconds>(
                        std::chrono::steady_clock::now().time_since_epoch()).count();
                    
                    // 调用回调函数
                    try {
                        gpio_info.callback(gpio_info.config.pin, current_value, timestamp);
                    } catch (const std::exception& e) {
                        log_handler_->error("GPIO {} callback exception: {}", 
                                          gpio_info.config.pin, e.what());
                    }
                    
                    log_handler_->debug("GPIO {} triggered: {} -> {}", 
                                       gpio_info.config.pin, last_value, current_value);
                }
                
                // 更新状态
                gpio_info.last_value = current_value;
            }
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(MONITOR_INTERVAL_MS));
    }
    
    log_handler_->info("GPIO monitor loop stopped");
}

bool GpioManager::init_single_gpio(const GpioConfig& config) {
    // 检查GPIO引脚是否已经导出
    std::string gpio_path = get_gpio_path(config.pin);
    struct stat st;
    bool already_exported = (stat(gpio_path.c_str(), &st) == 0);
    
    if (!already_exported) {
        // 导出GPIO引脚
        std::string export_path = "/sys/class/gpio/export";
        std::ofstream export_file(export_path);
        if (!export_file.is_open()) {
            log_handler_->error("Cannot open GPIO export file: {}", export_path);
            return false;
        }
        
        export_file << config.pin;
        export_file.close();
        
        // 等待sysfs文件创建
        int retries = 10;
        bool success = false;
        while (retries > 0 && !success) {
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
            success = (stat(gpio_path.c_str(), &st) == 0);
            retries--;
        }
        
        if (!success) {
            log_handler_->error("Cannot create GPIO sysfs path: {}", gpio_path);
            return false;
        }
    }
    
    // 设置GPIO方向
    std::string direction_path = get_gpio_path(config.pin, "direction");
    std::string direction_str = (config.direction == GpioDirection::INPUT) ? "in" : "out";
    
    for (int i = 0; i < 3; i++) {
        std::ofstream direction_file(direction_path);
        if (direction_file.is_open()) {
            direction_file << direction_str;
            direction_file.close();
            break;
        }
        
        if (i == 2) {
            log_handler_->error("Cannot set GPIO direction: {}", direction_path);
            return false;
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
    }
    
    // 如果是输出GPIO，设置初始值
    if (config.direction == GpioDirection::OUTPUT) {
        std::string value_path = get_gpio_path(config.pin, "value");
        std::ofstream value_file(value_path);
        if (value_file.is_open()) {
            value_file << config.initial_value;
            value_file.close();
        } else {
            log_handler_->warn("Cannot set initial value for GPIO {}", config.pin);
        }
    }
    
    return true;
}

void GpioManager::cleanup_single_gpio(int pin) {
    std::string unexport_path = "/sys/class/gpio/unexport";
    std::ofstream unexport_file(unexport_path);
    if (!unexport_file.is_open()) {
        log_handler_->error("Cannot open GPIO unexport file: {}", unexport_path);
        return;
    }
    
    unexport_file << pin;
    unexport_file.close();
    
    log_handler_->debug("GPIO {} cleaned up", pin);
}

bool GpioManager::is_debounce_elapsed(GpioInfo& gpio_info) {
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
        now - gpio_info.last_trigger_time).count();
    
    if (elapsed >= gpio_info.config.debounce_time_ms) {
        gpio_info.last_trigger_time = now;
        return true;
    }
    
    return false;
}

std::string GpioManager::get_gpio_path(int pin, const std::string& attribute) {
    std::string path = "/sys/class/gpio/gpio" + std::to_string(pin);
    if (!attribute.empty()) {
        path += "/" + attribute;
    }
    return path;
}

} // namespace aby_box 