#include "module/video_engine/video_engine.hpp"
#include "module/video_engine/mpi/video_detector_engine.h"
#include "utils/api_client.h"
#include "uorb/subscription.h"
#include "uorb/abs_time.h"
#include <ctime>
#include <sstream>
#include <stdexcept>
#include <csignal>

// 全局变量，用于跨文件的线程间通信
std::atomic<aby_box::VideoEngine*> g_video_engine_instance{nullptr};

// 信号处理函数
static void camera_crash_handler(int sig) {
  // 防止重复处理相同信号
  static std::atomic<bool> signal_being_handled{false};
  static std::atomic<int> last_signal{0};
  static std::atomic<time_t> last_signal_time{0};
  
  time_t now = time(nullptr);
  time_t time_since_last = now - last_signal_time.load();
  
  // 如果相同信号在10秒内重复触发，忽略后续信号
  if (signal_being_handled.load() || 
      (last_signal.load() == sig && time_since_last < 10)) {
    return;
  }
  
  signal_being_handled.store(true);
  last_signal.store(sig);
  last_signal_time.store(now);
  
  if (auto* engine = g_video_engine_instance.load()) {
    engine->reportCameraError(aby_box::CameraErrorType::THREAD_CRASHED, 
                              "Camera thread crashed with signal: " + std::to_string(sig));
  }
  
  signal_being_handled.store(false);
}

namespace aby_box {

bool VideoEngine::init() { 
  log_handler_->info("Initializing VideoEngine with crash detection");
  
  // 注册全局实例
  g_video_engine_instance.store(this);
  
  // 安装信号处理器
  signal(SIGSEGV, camera_crash_handler);
  signal(SIGABRT, camera_crash_handler);
  signal(SIGFPE, camera_crash_handler);
  
  camera_failed_.store(false);
  log_handler_->info("VideoEngine initialized - camera errors will cause immediate shutdown");
  return true; 
}

bool VideoEngine::start() {
  log_handler_->info("Starting VideoEngine");

  if (is_running_.load()) {
    log_handler_->warn("VideoEngine already running");
    return true;
  }

  is_running_ = true;

  // 启动录制线程
  recording_thread_ = std::thread(&VideoEngine::recording_loop, this);
  pthread_setname_np(recording_thread_.native_handle(), "aby_vid_recording");

  // 启动视频模式监听线程
  video_mode_thread_ = std::thread(&VideoEngine::video_mode_subscriber, this);
  pthread_setname_np(video_mode_thread_.native_handle(), "aby_vid_mode");

  log_handler_->info("VideoEngine started successfully");
  return true;
}

bool VideoEngine::stop() {
  log_handler_->info("Stopping VideoEngine");
  is_running_ = false;
  g_video_engine_instance.store(nullptr);

  return true;
}

void VideoEngine::join() {
  if (recording_thread_.joinable()) {
    recording_thread_.join();
  }
  if (video_mode_thread_.joinable()) {
    video_mode_thread_.join();
  }
  log_handler_->info("VideoEngine threads joined");
}

void VideoEngine::recording_loop() {
  log_handler_->info("VideoEngine recording loop started");
  
  try {
    // launch_main 应该持续运行，正常情况下不会返回
    // 如果返回了，无论返回值是什么，都说明出现了问题
    log_handler_->info("Starting camera main function");
    int result = launch_main();
    
    // 如果我们到达这里，说明launch_main退出了
    if (result == 0) {
      log_handler_->warn("launch_main exited normally, this indicates a problem");
      reportCameraError(CameraErrorType::UNKNOWN_ERROR, 
                       "launch_main unexpectedly exited with success code");
    } else {
      log_handler_->error("launch_main exited with error code: {} (likely VI init failed)", result);
      if (result == -1) {
        // launch_main返回-1通常表示VI初始化失败
        reportCameraError(CameraErrorType::INITIALIZATION_FAILED, 
                         "Camera initialization failed (VI init failed)");
      } else {
        reportCameraError(CameraErrorType::API_CALL_FAILED, 
                         "launch_main returned error code: " + std::to_string(result));
      }
    }
    
  } catch (const std::exception& e) {
    reportCameraError(CameraErrorType::UNKNOWN_ERROR, 
                     std::string("Exception in recording loop: ") + e.what());
  } catch (...) {
    reportCameraError(CameraErrorType::UNKNOWN_ERROR, 
                     "Unknown exception in recording loop");
  }
  
  log_handler_->info("VideoEngine recording loop exited");
}

void VideoEngine::reportCameraError(CameraErrorType error_type, const std::string& error_msg) {
  std::lock_guard<std::mutex> lock(error_mutex_);
  
  // 防止重复报告
  if (camera_failed_.load()) {
    return;
  }
  
  camera_failed_.store(true);
  last_error_message_ = error_msg;
  
  logCameraError(error_type, error_msg);
  
  // 立即发送传感器错误状态到服务器
  std::thread([this, error_type, error_msg]() {
    try {
      // 构造错误码和错误消息（限制长度以符合数据库列限制）
      int error_code = static_cast<int>(error_type);
      std::string error_message = error_msg;
      if (error_message.length() > 50) {
        error_message = error_message.substr(0, 47) + "...";
      }
      
      // 根据错误类型添加额外信息（缩短字符串以符合数据库列长度限制）
      std::string additional_info;
      switch (error_type) {
        case CameraErrorType::INITIALIZATION_FAILED:
          additional_info = "Init failed";
          break;
        case CameraErrorType::SENSOR_DISCONNECTED:
          additional_info = "Sensor disconnected";
          break;
        case CameraErrorType::API_CALL_FAILED:
          additional_info = "API call failed";
          break;
        case CameraErrorType::MEMORY_ERROR:
          additional_info = "Memory error";
          break;
        case CameraErrorType::THREAD_CRASHED:
          additional_info = "Thread crashed";
          break;
        case CameraErrorType::UNKNOWN_ERROR:
          additional_info = "Unknown error";
          break;
        default:
          additional_info = "Unspecified error";
          break;
      }
      
      // 调用API上报传感器错误
      bool success = APIClient::getInstance().reportSensorError(
        "camera", error_code, error_message, additional_info);
      
      if (success) {
        log_handler_->info("Camera error successfully reported to server");
      } else {
        log_handler_->error("Failed to report camera error to server");
      }
    } catch (const std::exception& e) {
      log_handler_->error("Exception while reporting camera error to server: {}", e.what());
    } catch (...) {
      log_handler_->error("Unknown exception while reporting camera error to server");
    }
  }).detach(); // 使用detach避免阻塞主线程
  
  log_handler_->error("Camera failure detected - stopping VideoEngine module immediately");

  // 直接停止video_engine模块
  is_running_.store(false);
}

bool VideoEngine::isCameraHealthy() const {
  return !camera_failed_.load();
}

void VideoEngine::logCameraError(CameraErrorType error_type, const std::string& error_msg) {
  std::string error_type_str;
  
  switch (error_type) {
    case CameraErrorType::NONE:
      error_type_str = "NONE";
      break;
    case CameraErrorType::INITIALIZATION_FAILED:
      error_type_str = "INITIALIZATION_FAILED";
      break;
    case CameraErrorType::SENSOR_DISCONNECTED:
      error_type_str = "SENSOR_DISCONNECTED";
      break;
    case CameraErrorType::API_CALL_FAILED:
      error_type_str = "API_CALL_FAILED";
      break;
    case CameraErrorType::MEMORY_ERROR:
      error_type_str = "MEMORY_ERROR";
      break;
    case CameraErrorType::THREAD_CRASHED:
      error_type_str = "THREAD_CRASHED";
      break;
    case CameraErrorType::UNKNOWN_ERROR:
      error_type_str = "UNKNOWN_ERROR";
      break;
  }
  
  log_handler_->error("Camera Error [{}]: {}", error_type_str, error_msg);
}

void VideoEngine::video_mode_subscriber() {
  log_handler_->info("Starting video mode subscriber thread");

  uorb::SubscriptionData<uorb::msg::video_mode_control> sub_video_mode;
  uint8_t result = 0;
  int ioctl_ret = 0;
  int timeout_ms = 1000;  // 1秒超时，减少CPU占用

  struct orb_pollfd poll_fd = {
    .fd = sub_video_mode.handle(),
    .events = POLLIN,
    .revents = 0
  };

  int fd = open(DEVICE_PATH, O_RDWR);
  if (fd < 0) {
    log_handler_->error("Failed to open dark mode device: {}", DEVICE_PATH);
  } else {
    log_handler_->info("Dark mode device opened successfully");
  }

  while (is_running_.load()) {
    // 使用poll等待数据，避免忙等待
    if (0 < orb_poll(&poll_fd, 1, timeout_ms)) {
      if ((poll_fd.revents & POLLIN) && sub_video_mode.Update()) {
        auto data = sub_video_mode.get();

        bool new_gray_mode = (data.video_mode == VIDEO_MODE_GRAY);
        bool old_gray_mode = gray_mode_enabled_.load();

        if (new_gray_mode != old_gray_mode) {
          gray_mode_enabled_.store(new_gray_mode);
          log_handler_->info("Video mode changed to: {} (timestamp: {})",
                              new_gray_mode ? "GRAY" : "COLOR", data.timestamp);

          if (data.video_mode == VIDEO_MODE_GRAY) {
            ioctl_ret = ioctl(fd, DARK_SET_HIGH, &result);
            if (ioctl_ret == 0) {
              log_handler_->info("Dark mode set to HIGH for GRAY mode, result: {}", result);
            } else {
              log_handler_->error("Failed to set dark mode HIGH, ioctl returned: {}", ioctl_ret);
            }
          } else if (data.video_mode == VIDEO_MODE_COLOR) {
            ioctl_ret = ioctl(fd, DARK_SET_LOW, &result);
            if (ioctl_ret == 0) {
              log_handler_->info("Dark mode set to LOW for COLOR mode, result: {}", result);
            } else {
              log_handler_->error("Failed to set dark mode LOW, ioctl returned: {}", ioctl_ret);
            }
          }
        }
      }
    }
    // poll超时后会自动继续循环，无需额外sleep
  }

  log_handler_->info("Video mode subscriber thread exited");
  }

} // namespace aby_box