#include "module/video_engine/mpi/image_capture_queue.h"
#include <algorithm>
#include <cstdlib>
#include <cstdio>
#include <cmath>

// ImageCaptureQueue 实现
ImageCaptureQueue& ImageCaptureQueue::getInstance() {
  static ImageCaptureQueue instance;
  return instance;
}

void ImageCaptureQueue::pushFrame(const ImageCaptureData& data) {
  std::lock_guard<std::mutex> lock(mutex_);
  
  if (shutdown_) {
    return;
  }
  
  // 如果队列满了，丢弃最旧的帧
  if (queue_.size() >= MAX_QUEUE_SIZE) {
    // 释放最旧帧的内存
    if (!queue_.empty()) {
      ImageCaptureData oldData = queue_.front();
      queue_.pop();
      if (oldData.pYData) free(oldData.pYData);
      if (oldData.pUVData) free(oldData.pUVData);
    }
  }
  
  queue_.push(data);
  cv_.notify_one();
}

bool ImageCaptureQueue::popFrame(ImageCaptureData& data) {
  std::unique_lock<std::mutex> lock(mutex_);
  
  cv_.wait(lock, [this] { return !queue_.empty() || shutdown_; });
  
  if (shutdown_ && queue_.empty()) {
    return false;
  }
  
  if (!queue_.empty()) {
    data = queue_.front();
    queue_.pop();
    return true;
  }
  
  return false;
}

void ImageCaptureQueue::shutdown() {
  std::lock_guard<std::mutex> lock(mutex_);
  shutdown_ = true;
  
  // 清理队列中剩余的数据
  while (!queue_.empty()) {
    ImageCaptureData data = queue_.front();
    queue_.pop();
    if (data.pYData) free(data.pYData);
    if (data.pUVData) free(data.pUVData);
  }
  
  cv_.notify_all();
}

bool ImageCaptureQueue::shouldProcessConfidence(float score) {
  // 简化逻辑：只检查基本的置信度阈值
  // 具体的置信度排序和选择由ImageCaptureManager处理
  static constexpr float MIN_SCORE_THRESHOLD = 0.3f; // 最低置信度阈值
  return score >= MIN_SCORE_THRESHOLD;
}

void ImageCaptureQueue::clearConfidenceData() {
  std::lock_guard<std::mutex> lock(confidence_mutex_);
  top_confidences_.clear();
} 