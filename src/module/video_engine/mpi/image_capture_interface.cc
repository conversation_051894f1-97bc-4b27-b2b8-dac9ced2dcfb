#include "module/video_engine/mpi/image_capture_interface.h"
#include "module/image_capture_manager.hpp"
#include <memory>

namespace {
    std::unique_ptr<aby_box::ImageCaptureManager> g_image_capture_manager;
}

extern "C" {

int init_image_capture_manager(void) {
    try {
        g_image_capture_manager = std::make_unique<aby_box::ImageCaptureManager>("ImageCaptureManager");
        
        if (!g_image_capture_manager->init()) {
            g_image_capture_manager.reset();
            return -1;
        }
        
        if (!g_image_capture_manager->start()) {
            g_image_capture_manager.reset();
            return -1;
        }
        
        return 0;
    } catch (const std::exception& e) {
        g_image_capture_manager.reset();
        return -1;
    }
}

void destroy_image_capture_manager(void) {
    if (g_image_capture_manager) {
        g_image_capture_manager->stop();
        g_image_capture_manager->join();
        g_image_capture_manager.reset();
    }
}

void capture_image_frame(const uint8_t* frame_data, uint32_t width, uint32_t height, 
                        float confidence, uint64_t timestamp) {
    if (g_image_capture_manager && frame_data) {
        g_image_capture_manager->capture_frame(frame_data, width, height, confidence, timestamp);
    }
}

} // extern "C" 