// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "module/cloud_sync_manager.hpp"
#include "module/video_engine/mpi/video_detector_engine.h"
#include "utils/api_client.h"
#include "utils/thread_safety.hpp"
#include "utils/safe_file_ops.hpp"
#include <ctime>
#include <fstream>
#include <nlohmann/json.hpp>
#include <poll.h>
#include <filesystem>
#include <sstream>

namespace aby_box {

CloudSyncManager::CloudSyncManager(const std::string &module_name)
    : BaseModule(module_name), is_running_(false), recordings_path_("/mnt/recordings/"), json_filename_("video_data.json") {}

CloudSyncManager::~CloudSyncManager() {
  stop();
  join();
}

bool CloudSyncManager::init() {
  log_handler_->info("Initializing CloudSyncManager module");

  // Reset the last processed timestamp
  std::lock_guard<std::mutex> lock(last_timestamp_mutex_);
  last_processed_timestamp_ = 0;

  return true;
}

bool CloudSyncManager::start() {
  log_handler_->info("Starting CloudSyncManager module");

  if (is_running_.load()) {
    log_handler_->warn("CloudSyncManager already running");
    return true;
  }

  is_running_ = true;
  
  // Start the weight data listener thread
  listener_thread_ = std::make_unique<std::thread>(
      &CloudSyncManager::weight_data_listener, this);
  // 设置线程名称
  pthread_setname_np(listener_thread_->native_handle(), "aby_cloud_sync");
  
  // Start the heartbeat sender thread
  heartbeat_thread_ = std::make_unique<std::thread>(
      &CloudSyncManager::heartbeat_sender, this);
  // 设置线程名称
  pthread_setname_np(heartbeat_thread_->native_handle(), "aby_heartbeat");

  return true;
}

bool CloudSyncManager::stop() {
  log_handler_->info("Stopping CloudSyncManager module");
  is_running_ = false;
  return true;
}

void CloudSyncManager::join() {
  if (listener_thread_ && listener_thread_->joinable()) {
    listener_thread_->join();
    log_handler_->debug("CloudSyncManager listener thread joined");
  }
  
  if (heartbeat_thread_ && heartbeat_thread_->joinable()) {
    heartbeat_thread_->join();
    log_handler_->debug("CloudSyncManager heartbeat thread joined");
  }
}

void CloudSyncManager::weight_data_listener() {
  log_handler_->info("Weight data listener thread started");

  // Subscribe to weight_calculate messages
  uorb::SubscriptionData<uorb::msg::weight_calculate> sub_weight_calculate;

  // Setup polling
  int timeout_ms = 100; // 100ms timeout for polling

  struct orb_pollfd poll_fd = {
      .fd = sub_weight_calculate.handle(), .events = POLLIN, .revents = 0};

  log_handler_->debug("Weight data listener polling started");
  
  // 使用成员变量而非局部变量
  
  // 确保基础目录存在
  std::error_code ec;
  std::filesystem::create_directories(recordings_path_, ec);
  if (ec) {
    log_handler_->error("无法创建基础录制目录: {} - {}", recordings_path_, ec.message());
    // 继续执行，后续可能会重试创建子目录
  }

  // Polling loop
  while (is_running_) {
    // Poll for new data with timeout
    if (0 < orb_poll(&poll_fd, 1, timeout_ms)) {
      // Check if this is a weight_calculate message
      if ((poll_fd.revents & POLLIN) && sub_weight_calculate.Update()) {
        // Get the message data
        auto weight_data = sub_weight_calculate.get();

        // ===== 处理重量计算数据开始 =====

        // 检查消息是否已处理过（避免重复）
        {
          SAFE_LOCK_NAMED(last_timestamp_mutex_, "CloudSyncManager::weight_data_listener");
          if (weight_data.timestamp_leave <= last_processed_timestamp_) {
            log_handler_->debug("跳过重复或乱序消息: 当前={}, 上一个={}",
                                weight_data.timestamp_leave,
                                last_processed_timestamp_);
            continue; // 跳过本次循环，处理下一条消息
          }
          last_processed_timestamp_ = weight_data.timestamp_leave;
        }

        // 记录接收到的重量数据
        log_handler_->info("接收到重量计算数据:");
        log_handler_->info(" - 猫砂重量: {:.2f}g", weight_data.weight_litter);
        log_handler_->info(" - 猫的重量: {:.2f}g", weight_data.weight_cat);
        log_handler_->info(" - 猫屎重量: {:.2f}g", weight_data.weight_shit);

        // 将重量从克转换为千克以便记录
        double litter_kg = weight_data.weight_litter / 1000.0;
        double cat_kg = weight_data.weight_cat / 1000.0;
        double waste_kg = weight_data.weight_shit / 1000.0;

        try {
          // 直接使用时间戳作为秒级时间戳 (不需要除以1000或1000000)
          std::time_t ts_start = weight_data.timestamp_enter;
          
          // 转换时间戳为格式化的日期时间字符串
          std::tm tm_info;
          
          // 获取本地时间
          tm_info = *std::localtime(&ts_start);
          
          // 格式化为 YYYY-MM-DD_HH-MM-SS_hls
          char folder_name[64];
          std::strftime(folder_name, sizeof(folder_name), "%Y-%m-%d_%H-%M-%S_hls", &tm_info);
          
          // 使用格式化后的时间戳作为文件夹名
          std::string folder_path = recordings_path_ + std::string(folder_name);
          std::string file_path = folder_path + "/" + json_filename_;
          log_handler_->info("file_path : {} ", file_path);
          // 创建目录（如果不存在）
          std::error_code ec;
          std::filesystem::create_directories(folder_path, ec);
          if (ec) {
            log_handler_->error("创建目录失败: {} - {}", folder_path, ec.message());
            continue;
          }

          // 获取猫帧数统计数据
          uint32_t cat_frames = get_cat_frame_count();
          
          // 创建JSON数据
          nlohmann::json json_data = {
              {"timestamp_enter", weight_data.timestamp_enter},
              {"timestamp_leave", weight_data.timestamp_leave},
              {"weight_litter", litter_kg},
              {"weight_cat", cat_kg},
              {"weight_waste", waste_kg},
              {"device_id", weight_data.device_id},
              {"cat_frame_count", cat_frames}};
          
          // 重置猫帧数计数，为下一次猫进入做准备
          reset_cat_frame_count();

          // 使用安全文件操作写入JSON文件
          auto& safe_ops = getSafeFileOps();
          std::string json_content = json_data.dump(4); // 格式化JSON，缩进4个空格

          auto result = safe_ops.writeFileAtomic(file_path, json_content);
          if (result != FileOpResult::SUCCESS) {
            log_handler_->error("写入文件时发生错误: {} - {}",
                               file_path, SafeFileOps::getErrorString(result));
            continue;
          }

          // 使用安全文件操作创建标记文件，表示JSON文件已完全写入
          std::string marker_path = folder_path + "/.video_data_end";
          auto marker_result = safe_ops.writeFileAtomic(marker_path, "");

          if (marker_result != FileOpResult::SUCCESS) {
            log_handler_->error("创建标记文件失败: {} - {}",
                               marker_path, SafeFileOps::getErrorString(marker_result));
          } else {
            log_handler_->info("成功创建标记文件: {}", marker_path);
          }

          log_handler_->info("成功将数据写入: {}", file_path);
          // start to upload
          // 直接调用APIClient的同步方法
        } catch (const std::exception &e) {
          log_handler_->error("处理重量数据时出错: {}", e.what());
        }

        // ===== 处理重量计算数据结束 =====
      }
    }
  }

  log_handler_->info("Weight data listener thread stopped");
}

void CloudSyncManager::heartbeat_sender() {
  log_handler_->info("Heartbeat sender thread started");
  
  // Wait for API client to initialize
  while (is_running_ && !InitializationManager::getInstance().isInitialized()) {
    log_handler_->debug("Waiting for API client to initialize...");
    std::this_thread::sleep_for(std::chrono::seconds(5));
  }
  
  log_handler_->info("API client initialized, starting heartbeat cycle");
  
  // Main heartbeat loop
  while (is_running_) {
    // Check if OTA is in progress to adjust heartbeat interval
    unsigned int current_interval = heartbeat_interval_ms_;
    bool ota_in_progress = (ota_manager_ && ota_manager_->isUpgrading());
    
    // Sleep for heartbeat interval (breaking into smaller intervals to check is_running_)
    const unsigned int sleep_interval = 10000; // 10 seconds
    for (unsigned int i = 0; i < current_interval && is_running_; i += sleep_interval) {
      std::this_thread::sleep_for(std::chrono::milliseconds(std::min(sleep_interval, current_interval - i)));
      
      // If OTA status changed during sleep, adjust accordingly
      bool current_ota_status = (ota_manager_ && ota_manager_->isUpgrading());
      if (current_ota_status != ota_in_progress) {
        log_handler_->debug("OTA status changed during heartbeat interval, adjusting schedule");
        break; // Break early to recalculate interval
      }
    }
    
    if (!is_running_) break;
    
    // Send heartbeat, but only check for new OTA upgrades if not currently upgrading
    log_handler_->debug("Sending heartbeat...");
    auto response = APIClient::getInstance().sendHeartbeat();
    
    if (!(ota_manager_ && ota_manager_->isUpgrading())) {
      // Only check for new OTA upgrades if not currently upgrading
    checkOtaUpgrade(response);
    } else {
      log_handler_->debug("Skipping OTA upgrade check - upgrade already in progress");
    }
  }
  
  log_handler_->info("Heartbeat sender thread stopped");
}

bool CloudSyncManager::isCurrentTimeInUpdateWindow(int start_hour, int end_hour) const {
  // 如果开始时间大于等于结束时间，表示立即更新（不受时间限制）
  if (start_hour >= end_hour) {
    log_handler_->debug("OTA time window: immediate update (start_hour {} >= end_hour {})", start_hour, end_hour);
    return true;
  }
  
  // 获取当前时间
  auto now = std::chrono::system_clock::now();
  auto time_t = std::chrono::system_clock::to_time_t(now);
  std::tm* local_tm = std::localtime(&time_t);
  
  int current_hour = local_tm->tm_hour; // 24小时制，0-23
  
  log_handler_->debug("OTA time window check: current_hour={}, start_hour={}, end_hour={}", 
                     current_hour, start_hour, end_hour);
  
  // 检查当前时间是否在更新窗口内
  bool in_window = (current_hour >= start_hour && current_hour < end_hour);
  
  if (in_window) {
    log_handler_->info("Current time ({}) is within OTA update window ({}-{})", 
                      current_hour, start_hour, end_hour);
  } else {
    log_handler_->info("Current time ({}) is outside OTA update window ({}-{}), delaying update", 
                      current_hour, start_hour, end_hour);
  }
  
  return in_window;
}

bool CloudSyncManager::checkOtaUpgrade(const nlohmann::json &response) {
  // Check if we have a valid OTA manager reference
  if (!ota_manager_) {
    log_handler_->warn("OTA manager not set, cannot perform OTA upgrades");
    return false;
  }
  
  // Check if the device is already in OTA mode
  if (ota_manager_->isUpgrading()) {
    log_handler_->debug("Device already in OTA upgrade mode, skipping check");
    return false;
  }
  
  // Check if system is in OTA cooldown period (after reboot from previous failures)
  if (ota_manager_->isInSystemOtaCooldown()) {
    log_handler_->debug("System is in OTA cooldown period, skipping upgrade check");
    return false;
  }
  
  try {
    // Check if the response contains auto_ota_upgrade field
    if (response.is_object() && response.contains("auto_ota_upgrade")) {
      std::string auto_ota_upgrade = response["auto_ota_upgrade"].get<std::string>();
      
      if (auto_ota_upgrade == "on") {
        log_handler_->info("OTA upgrade flag set to 'on' in server response");
        
        // Check if the response contains version information
        if (response.contains("ota_version")) {
          std::string server_version = response["ota_version"].get<std::string>();
          
          // 首先检查是否已经处理过这个版本
          if (isOtaVersionAlreadyProcessed(server_version)) {
            log_handler_->debug("OTA version {} has already been processed, skipping", server_version);
            return false;
          }
          
          // 检查是否是上次失败的版本
          {
            std::lock_guard<std::mutex> lock(ota_failure_mutex_);
            log_handler_->debug("Checking OTA failure history - last_failed_ota_version_: '{}'", last_failed_ota_version_);
            log_handler_->debug("Server version: '{}', last failed version: '{}'", server_version, last_failed_ota_version_);
            
            if (!last_failed_ota_version_.empty() && server_version == last_failed_ota_version_) {
              log_handler_->warn("Skipping OTA upgrade for version {} - this version failed previously", server_version);
              // 标记为已处理，避免重复检查失败版本
              markOtaVersionAsProcessed(server_version);
              return false;
            }
            
            // 如果服务器版本与失败版本不同，清除失败记录
            if (!last_failed_ota_version_.empty() && server_version != last_failed_ota_version_) {
              log_handler_->info("New version {} detected, clearing previous failure record for version {}", 
                                server_version, last_failed_ota_version_);
              last_failed_ota_version_.clear();
            }
          }
          
          // Read the local version from config file
          std::string local_version;
          try {
            std::ifstream config_file("/etc/cfg/aby_box/config.json");
            if (config_file.is_open()) {
              nlohmann::json config_json = nlohmann::json::parse(config_file);
              if (config_json.contains("version_id")) {
                local_version = config_json["version_id"].get<std::string>();
              }
              config_file.close();
            }
          } catch (const std::exception &e) {
            log_handler_->error("Error reading local version from config: {}", e.what());
            local_version = "0.0.0"; // 设置默认版本，确保会进行升级
          }
          
          log_handler_->info("Version comparison: server={}, local={}", server_version, local_version);
          
          // Function to compare version strings (returns true if v1 > v2)
          auto compare_versions = [this](const std::string &v1, const std::string &v2) -> bool {
            try {
            // Split version strings into components
            std::vector<int> v1_components;
            std::vector<int> v2_components;
            
            std::istringstream v1_stream(v1);
            std::istringstream v2_stream(v2);
            
            std::string component;
            while (std::getline(v1_stream, component, '.')) {
              v1_components.push_back(std::stoi(component));
            }
            
            while (std::getline(v2_stream, component, '.')) {
              v2_components.push_back(std::stoi(component));
            }
            
            // Compare each component
            size_t max_size = std::max(v1_components.size(), v2_components.size());
            for (size_t i = 0; i < max_size; i++) {
              int v1_comp = (i < v1_components.size()) ? v1_components[i] : 0;
              int v2_comp = (i < v2_components.size()) ? v2_components[i] : 0;
              
              if (v1_comp > v2_comp) {
                return true;  // v1 is greater
              } else if (v1_comp < v2_comp) {
                return false; // v1 is less
              }
              // If components are equal, continue to next component
            }
            
            return false; // Versions are equal
            } catch (const std::exception &e) {
              log_handler_->error("Error comparing versions: {}", e.what());
              return true; // 比较失败时，倾向于进行升级
            }
          };
          
          // Only perform OTA if server version is greater than local version
          if (compare_versions(server_version, local_version)) {
            log_handler_->info("Server version {} is newer than local version {}, starting OTA upgrade", 
                              server_version, local_version);
            
            // Check if ota_required field exists and is true (optional field)
            bool ota_required = true; // Default to true if field doesn't exist
            if (response.contains("ota_required") && response["ota_required"].is_boolean()) {
              ota_required = response["ota_required"].get<bool>();
            }
            
            if (ota_required) {
              // 检查时间窗口限制
              int idle_update_start_hour = 0;  // 默认值：立即更新
              int idle_update_end_hour = 0;    // 默认值：立即更新
              
              // 从心跳包响应中读取时间窗口配置
              if (response.contains("idle_update_start_hour") && response["idle_update_start_hour"].is_number()) {
                idle_update_start_hour = response["idle_update_start_hour"].get<int>();
              }
              if (response.contains("idle_update_end_hour") && response["idle_update_end_hour"].is_number()) {
                idle_update_end_hour = response["idle_update_end_hour"].get<int>();
              }
              
              log_handler_->info("OTA time window configuration: start={}h, end={}h", 
                               idle_update_start_hour, idle_update_end_hour);
              
              // 检查当前时间是否在更新窗口内
              if (!isCurrentTimeInUpdateWindow(idle_update_start_hour, idle_update_end_hour)) {
                log_handler_->info("OTA upgrade for version {} delayed due to time window restriction", server_version);
                return false;
              }
              
              // Extract OTA URL if provided
              std::string ota_url;
              if (response.contains("ota_url") && response["ota_url"].is_string()) {
                ota_url = response["ota_url"].get<std::string>();
                log_handler_->info("OTA URL provided: {}", ota_url);
              }
              
              // Extract OTA MD5 URL if provided
              std::string ota_md5_url;
              if (response.contains("ota_md5_url") && response["ota_md5_url"].is_string()) {
                ota_md5_url = response["ota_md5_url"].get<std::string>();
                log_handler_->info("OTA MD5 URL provided: {}", ota_md5_url);
              }
              
              // Pass server version, OTA URL and MD5 URL to OTA manager for reference
              if (ota_manager_->startUpgrade(server_version, ota_url, ota_md5_url)) {
                log_handler_->info("OTA upgrade process started successfully for version {}", server_version);
                
                // 标记此版本为已处理，避免重复触发
                markOtaVersionAsProcessed(server_version);
                
              return true;
            } else {
              log_handler_->error("Failed to start OTA upgrade process");
                return false;
              }
            } else {
              log_handler_->info("Server has newer version {} but ota_required is false, skipping upgrade", server_version);
              return false;
            }
          } else if (server_version == local_version) {
            log_handler_->info("Server version {} matches local version {}, no upgrade needed", 
                              server_version, local_version);
            // 标记为已处理，避免重复检查相同版本
            markOtaVersionAsProcessed(server_version);
            return false;
          } else {
            log_handler_->info("Server version {} is older than local version {}, no upgrade needed", 
                              server_version, local_version);
            // 标记为已处理，避免重复检查旧版本
            markOtaVersionAsProcessed(server_version);
            return false;
          }
        } else {
          log_handler_->warn("auto_ota_upgrade is 'on' but server response missing ota_version field, cannot proceed with OTA");
          return false;
        }
      } else {
        log_handler_->debug("auto_ota_upgrade is '{}', no OTA upgrade required", auto_ota_upgrade);
        return false;
      }
    } else {
      log_handler_->debug("Response does not contain auto_ota_upgrade information");
      return false;
    }
  } catch (const std::exception &e) {
    log_handler_->error("Error checking for OTA upgrade: {}", e.what());
    return false;
  }
}

void CloudSyncManager::notifyOtaFailure(const std::string &failed_version) {
  std::lock_guard<std::mutex> lock(ota_failure_mutex_);
  log_handler_->info("Recording OTA failure for version: {}", failed_version);
  log_handler_->debug("Previous failed version was: '{}'", last_failed_ota_version_);
  last_failed_ota_version_ = failed_version;
  log_handler_->warn("OTA failure recorded - version {} is now marked as failed", failed_version);
}

void CloudSyncManager::clearOtaFailureRecord() {
  std::lock_guard<std::mutex> lock(ota_failure_mutex_);
  if (!last_failed_ota_version_.empty()) {
    last_failed_ota_version_.clear();
  }
}

void CloudSyncManager::markOtaVersionAsProcessed(const std::string &version) {
  std::lock_guard<std::mutex> lock(ota_processed_mutex_);
  last_processed_ota_version_ = version;
}

bool CloudSyncManager::isOtaVersionAlreadyProcessed(const std::string &version) const {
  std::lock_guard<std::mutex> lock(ota_processed_mutex_);
  bool already_processed = (!last_processed_ota_version_.empty() && 
                           last_processed_ota_version_ == version);
  return already_processed;
}

void CloudSyncManager::clearProcessedOtaVersion() {
  std::lock_guard<std::mutex> lock(ota_processed_mutex_);
  if (!last_processed_ota_version_.empty()) {
    last_processed_ota_version_.clear();
  }
}

} // namespace aby_box
