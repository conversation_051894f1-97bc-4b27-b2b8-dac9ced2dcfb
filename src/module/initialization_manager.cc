// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "module/initialization_manager.hpp"
#include <chrono>
#include <filesystem>
#include <fstream>
#include <random>
#include <spdlog/spdlog.h>
#include <unistd.h>  // for sync()
#include <cstdlib>   // for system()
#include <sstream>   // for std::stringstream
#include <iomanip>   // for std::setfill, std::setw

namespace aby_box {

// WriteCallback 函数实现
size_t InitializationManager::WriteCallback(void *contents, size_t size, size_t nmemb,
                                           std::string *userp) {
  userp->append((char *)contents, size * nmemb);
  return size * nmemb;
}

// Singleton instance getter
InitializationManager &InitializationManager::getInstance() {
  static InitializationManager instance;
  return instance;
}

// Constructor
InitializationManager::InitializationManager() : BaseModule("InitializationManager") {}

bool InitializationManager::init() {
  log_handler_ = std::make_shared<LogHandler>("InitializationManager");
  
  // 读取配置
  loadConfigValues();
  
  // 检查必要的配置
  if (hardware_sn_.empty()) {
    log_handler_->warn("hardware_sn is empty in config.json, generating new SN with format: 8HEX_PCB8HEX_TIMESTAMP");

    // 生成SN号格式: 8个16进制数字_PCB8个16进制数字_时间戳
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<uint32_t> dis(0, 0xFFFFFFFF); // 32位随机数用于生成16进制

    // 生成第一部分：8个16进制数字
    uint32_t first_part = dis(gen);
    std::stringstream first_hex;
    first_hex << std::hex << std::uppercase << std::setfill('0') << std::setw(8) << first_part;

    // 生成第二部分：PCB + 8个16进制数字
    uint32_t second_part = dis(gen);
    std::stringstream second_hex;
    second_hex << "PCB" << std::hex << std::uppercase << std::setfill('0') << std::setw(8) << second_part;

    // 生成第三部分：当前时间戳（毫秒）
    auto now = std::chrono::system_clock::now();
    auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();

    // 组合完整的SN号
    std::string generated_hardware_sn = first_hex.str() + "_" + second_hex.str() + "_" + std::to_string(timestamp);
    log_handler_->info("Generated hardware_sn: {}", generated_hardware_sn);
    
    // 使用ConfigManager保存到配置文件
    auto &config_manager = ConfigManager::getInstance();
    if (config_manager.setConfigValue("hardware_sn", generated_hardware_sn)) {
      hardware_sn_ = generated_hardware_sn;
      log_handler_->info("Successfully saved generated hardware_sn to config file");
    } else {
      log_handler_->error("Failed to save generated hardware_sn to config file");
      return false;
    }
  }
  
  // 初始化 curl
  curl_global_init(CURL_GLOBAL_ALL);
  
  log_handler_->info("InitializationManager initialized successfully");
  return true;
}

bool InitializationManager::start() {
  if (is_running_) {
    log_handler_->warn("InitializationManager is already running");
    return false;
  }
  
  is_running_ = true;
  
  // 启动初始化线程
  initialization_thread_ = std::make_unique<std::thread>(&InitializationManager::initialization_loop, this);
  pthread_setname_np(initialization_thread_->native_handle(), "aby_init_manager_");
  
  log_handler_->info("InitializationManager started");
  return true;
}

bool InitializationManager::stop() {
  if (!is_running_) {
    return true;
  }
  
  is_running_ = false;
  log_handler_->info("InitializationManager stopping");
  
  // Join the thread immediately in singleton mode
  if (initialization_thread_ && initialization_thread_->joinable()) {
    initialization_thread_->join();
    initialization_thread_.reset();
  }
  
  return true;
}

void InitializationManager::join() {
  // In singleton mode, thread is already joined in stop()
  if (!is_running_) {
    curl_global_cleanup();
    log_handler_->info("InitializationManager joined");
  }
}

void InitializationManager::initialization_loop() {
  bool initialization_phase = true;
  
  while (is_running_) {
    if (initialization_phase) {
      log_handler_->info("Starting API initialization check for user ID and device ID");
    }
    
    // 每次循环开始时都重新加载配置，确保获取最新数据
    loadConfigValues();
    
    if (initialization_phase) {
      log_handler_->debug("Loaded latest config values: user_id={}, device_id={}, hardware_sn={}", 
                          user_id_, device_id_, hardware_sn_);
    }
    
    // 如果还在初始化阶段，检查必要的条件
    if (initialization_phase && !api_init_complete_) {
      // 首先，我们需要确保有 user_id 可用
      if (user_id_.empty()) {
        log_handler_->info("No user ID available, attempting to fetch from server using hardware SN");
        if (!fetch_user_id_by_hardware_sn()) {
          log_handler_->error("Failed to fetch user ID from server. Cannot proceed with API initialization");
          std::this_thread::sleep_for(std::chrono::seconds(5));
          continue;
        }
        
        // 获取成功后，重新加载配置以确保获取最新的 user_id
        loadConfigValues();
        log_handler_->info("User ID fetched successfully, reloaded config: user_id={}", user_id_);
      }
      
      // 如果 device_id 为空，注册设备并获取它
      if (device_id_.empty()) {
        log_handler_->info("Device ID is empty, attempting to register device");
        if (!register_device()) {
          log_handler_->error("Failed to register device");
          std::this_thread::sleep_for(std::chrono::seconds(5));
          continue;
        }
        
        // 设备注册成功后，重新加载配置以确保获取最新的 device_id
        loadConfigValues();
        log_handler_->info("Device registered successfully, reloaded config: device_id={}", device_id_);
      }

      api_init_complete_ = true;
      initialization_phase = false;
      log_handler_->info("API initialization completed successfully: user_id={}, device_id={}", 
                         user_id_, device_id_);
    }
    
    // 初始化完成后，定期检查配置更新（每30秒）
    if (api_init_complete_) {
      std::this_thread::sleep_for(std::chrono::seconds(30));
    } else {
      std::this_thread::sleep_for(std::chrono::seconds(5));
    }
  }
}

bool InitializationManager::register_device() {
  std::string api_url = base_url_ + "/api/devices/register";
  std::string response;

  // 构建请求体
  nlohmann::json request_body = {
      {"user_id", user_id_},          {"hardware_sn", hardware_sn_},
      {"name", "客厅猫厕所"},         {"model", "CT-2024-Pro"},
      {"firmware_version", "v1.0.0"}, {"status", 1}};
  std::string request_data = request_body.dump();
  log_handler_->info("request_data: {}", request_data);
  
  long http_code = 0;
  CURLcode res = performCurlRequest(api_url, "POST", request_data, response, &http_code);

  if (res != CURLE_OK) {
    log_handler_->error("Failed to register device: {}", curl_easy_strerror(res));
    return false;
  }

  if (http_code < 200 || http_code >= 300) {
    log_handler_->error("Server returned error code {}: {}", http_code, response);
    return false;
  }

  try {
    auto json = nlohmann::json::parse(response);
    device_id_ = json["device_id"].get<std::string>();

    // 使用ConfigManager更新config文件
    auto &config_manager = ConfigManager::getInstance();
    if (config_manager.setConfigValue("device_id", device_id_)) {
      return true;
    }
  } catch (const std::exception &e) {
    log_handler_->error("Failed to parse API response: {}", e.what());
  }

  return false;
}

bool InitializationManager::fetch_user_id_by_hardware_sn() {
  if (hardware_sn_.empty()) {
    log_handler_->error("Cannot fetch user ID: hardware_sn is empty");
    return false;
  }

  log_handler_->info("Fetching user ID for hardware SN: {}", hardware_sn_);
  
  // 使用 APIClient 获取 user_id
  std::string fetched_user_id = APIClient::getInstance().getUserIdByHardwareSn(hardware_sn_);
  
  if (fetched_user_id.empty()) {
    log_handler_->error("Failed to fetch user ID for hardware SN: {}", hardware_sn_);
    return false;
  }
  
  // 更新本地变量
  user_id_ = fetched_user_id;
  
  // 使用 ConfigManager 保存到配置文件
  auto &config_manager = ConfigManager::getInstance();
  if (config_manager.setUserId(user_id_)) {
    log_handler_->info("Successfully fetched and saved user ID '{}' for hardware SN: {}", 
                       user_id_, hardware_sn_);
    return true;
  } else {
    log_handler_->error("Failed to save user ID '{}' to config file", user_id_);
    return false;
  }
}

void InitializationManager::loadConfigValues() {
  auto &config = ConfigManager::getInstance();
  
  // 保存旧值用于比较
  std::string old_device_id = device_id_;
  std::string old_user_id = user_id_;
  std::string old_hardware_sn = hardware_sn_;
  std::string old_base_url = base_url_;
  
  // 检查是否是首次加载（所有值都为空）
  bool is_first_load = old_device_id.empty() && old_user_id.empty() && 
                       old_hardware_sn.empty() && old_base_url.empty();
  
  // 加载最新配置值
  device_id_ = config.getConfigValue<std::string>("device_id", "");
  user_id_ = config.getConfigValue<std::string>("user_id", "");
  hardware_sn_ = config.getConfigValue<std::string>("hardware_sn", "");
  base_url_ = config.getmediamtxUrl();
  
  if (is_first_load) {
    // 首次加载时显示初始值
    log_handler_->info("Initial config values loaded: device_id='{}', user_id='{}', hardware_sn='{}', base_url='{}'", 
                       device_id_, user_id_, hardware_sn_, base_url_);
  } else {
    // 后续加载时只记录变化
    if (old_device_id != device_id_) {
      log_handler_->info("Device ID updated: '{}' -> '{}'", old_device_id, device_id_);
    }
    if (old_user_id != user_id_) {
      log_handler_->info("User ID updated: '{}' -> '{}'", old_user_id, user_id_);
    }
    if (old_hardware_sn != hardware_sn_) {
      log_handler_->info("Hardware SN updated: '{}' -> '{}'", old_hardware_sn, hardware_sn_);
    }
    if (old_base_url != base_url_) {
      log_handler_->info("Base URL updated: '{}' -> '{}'", old_base_url, base_url_);
    }
  }
}

CURLcode InitializationManager::performSimpleGetRequest(const std::string &url, std::string &response) {
  // 为每个请求创建一个新的 CURL 句柄，而不是使用共享的
  CURL *request_curl = curl_easy_init();
  if (!request_curl) {
    log_handler_->error("Failed to initialize CURL handle for request");
    return CURLE_FAILED_INIT;
  }

  char error_buffer[CURL_ERROR_SIZE];

  curl_easy_setopt(request_curl, CURLOPT_URL, url.c_str());
  curl_easy_setopt(request_curl, CURLOPT_WRITEFUNCTION, WriteCallback);
  curl_easy_setopt(request_curl, CURLOPT_WRITEDATA, &response);
  curl_easy_setopt(request_curl, CURLOPT_TIMEOUT, 5L);
  curl_easy_setopt(request_curl, CURLOPT_ERRORBUFFER, error_buffer);

  CURLcode res = curl_easy_perform(request_curl);
  curl_easy_cleanup(request_curl);

  if (res != CURLE_OK) {
    log_handler_->error("CURL request failed: {}", error_buffer);
  }

  return res;
}

CURLcode InitializationManager::performCurlRequest(const std::string &url, const std::string &method,
                                                  const std::string &postData, std::string &response,
                                                  long *http_code) {
  // 为此请求创建一个新的 CURL 句柄
  CURL *thread_curl = curl_easy_init();
  if (!thread_curl) {
    log_handler_->error("Failed to initialize CURL handle");
    return CURLE_FAILED_INIT;
  }

  char error_buffer[CURL_ERROR_SIZE];
  struct curl_slist *headers = NULL;
  headers = curl_slist_append(headers, "Content-Type: application/json");

  curl_easy_setopt(thread_curl, CURLOPT_URL, url.c_str());
  curl_easy_setopt(thread_curl, CURLOPT_HTTPHEADER, headers);
  curl_easy_setopt(thread_curl, CURLOPT_WRITEFUNCTION, WriteCallback);
  curl_easy_setopt(thread_curl, CURLOPT_WRITEDATA, &response);
  curl_easy_setopt(thread_curl, CURLOPT_TIMEOUT, 5L);
  curl_easy_setopt(thread_curl, CURLOPT_ERRORBUFFER, error_buffer);

  if (method == "POST") {
    curl_easy_setopt(thread_curl, CURLOPT_POSTFIELDS, postData.c_str());
  }

  CURLcode res = curl_easy_perform(thread_curl);
  
  if (http_code) {
    curl_easy_getinfo(thread_curl, CURLINFO_RESPONSE_CODE, http_code);
  }

  if (res != CURLE_OK) {
    log_handler_->error("CURL request failed: {}", error_buffer);
  }

  curl_slist_free_all(headers);
  curl_easy_cleanup(thread_curl);

  return res;
}

} // namespace aby_box 