// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include <fstream>
#include <thread>
#include <curl/curl.h>
#include <nlohmann/json.hpp>

#include "module/statistics.hpp"

namespace aby_box {

Statistics::Statistics(const std::string &module_name) 
    : BaseModule(module_name),
      boot_time_(std::chrono::system_clock::now()),
      network_ready_(false),
      timezone_verified_(false) {}

Statistics::~Statistics() {
  stop();
  join();
}

bool Statistics::init() {
  timezone_ = get_system_timezone();
  log_handler_->info("Initial system timezone: {}", timezone_);
  return true;
}

bool Statistics::start() {
  if (is_running_) {
    return true;
  }
  
  is_running_ = true;
  stats_thread_ = std::make_unique<std::thread>(&Statistics::stats_loop, this);
  // 设置线程名称
  pthread_setname_np(stats_thread_->native_handle(), "aby_stats");
  return true;
}

bool Statistics::stop() {
  is_running_ = false;
  return true;
}

void Statistics::join() {
  if (stats_thread_ && stats_thread_->joinable()) {
    stats_thread_->join();
  }
}

std::string Statistics::get_timezone() const {
  return timezone_;
}

time_t Statistics::get_boot_timestamp() const {
  return std::chrono::system_clock::to_time_t(boot_time_);
}

uint64_t Statistics::get_uptime() const {
  return uptime_seconds_.load();
}

void Statistics::stats_loop() {
  log_handler_->info("Statistics loop started");
  
  // Wait for InitializationManager to complete initialization
  while (is_running_ && !InitializationManager::getInstance().isInitialized()) {
    log_handler_->debug("Waiting for InitializationManager to initialize...");
    std::this_thread::sleep_for(std::chrono::seconds(10));
  }
  
  if (!is_running_) {
    log_handler_->info("Statistics loop stopped during initialization wait");
    return;
  }
  
  log_handler_->info("InitializationManager initialized, starting statistics tasks");
  
  while (is_running_) {
    auto check_interval = network_ready_ ? 
                         SLOW_CHECK_INTERVAL_MS : 
                         FAST_CHECK_INTERVAL_MS;

    if (network_check()) {
      // 首次连接网络且未验证时区
      if (!timezone_verified_) {
        if (verify_timezone()) {
          timezone_verified_ = true;
          log_handler_->info("Timezone verification completed");
          boot_time_ = std::chrono::system_clock::now() - 
                       std::chrono::seconds(uptime_seconds_.load());
        }
      }
    }

    auto now = std::chrono::system_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(
        now - boot_time_).count();
    uptime_seconds_.store(duration);
    
    std::this_thread::sleep_for(std::chrono::milliseconds(check_interval));
  }
}

bool Statistics::network_check() {
  // 使用ping检测网络连接
  std::string cmd = "ping -c 5 -W 2 ******* > /dev/null 2>&1";
  bool is_connected = (system(cmd.c_str()) == 0);
  
  if (is_connected != network_ready_) {
    network_ready_ = is_connected;
    if (is_connected) {
      log_handler_->info("Network connection established");
    }
  }
  return is_connected;
}

bool Statistics::verify_timezone() {
  std::string ip_tz = APIClient::getInstance().get_ip_timezone();
  if (ip_tz.empty()) {
    log_handler_->error("Failed to get timezone from IP");
    return false;
  }
  log_handler_->info("Timezone from IP: {}", ip_tz);

  if (ip_tz != timezone_) {
    log_handler_->info("Timezone mismatch. Local: {}, IP-based: {}", 
                      timezone_, ip_tz);
    update_timezone(ip_tz);
  }
  log_handler_->info("Timezone verified: {}", timezone_);
  
  // Send the verified timezone to server
  if (APIClient::getInstance().send_device_timezone(timezone_)) {
      log_handler_->info("Timezone sent to server successfully");
  } else {
      log_handler_->error("Failed to send timezone to server");
      // Continue with verification process even if sending failed
  }
  
  return true;
  }

void Statistics::update_timezone(const std::string& new_tz) {
  // 构建时区文件路径
  std::string zoneinfo_path = "/usr/share/zoneinfo/" + new_tz;
  
  // 验证时区文件是否存在
  if (access(zoneinfo_path.c_str(), F_OK) != 0) {
    log_handler_->error("Timezone file does not exist: {}", zoneinfo_path);
    return;
  }

  // 备份当前的localtime
  if (access("/etc/localtime", F_OK) == 0) {
    if (unlink("/etc/localtime") != 0) {
      log_handler_->error("Failed to remove old timezone link");
      return;
    }
  }

  // 创建新的符号链接
  if (symlink(zoneinfo_path.c_str(), "/etc/localtime") != 0) {
    log_handler_->error("Failed to create new timezone link");
    return;
  }

  // 更新/etc/timezone文件
  std::ofstream tz_file("/etc/timezone");
  if (tz_file) {
    tz_file << new_tz << std::endl;
    tz_file.close();
  }

  timezone_ = new_tz;
  boot_time_ = std::chrono::system_clock::now();
  log_handler_->info("Updated timezone to: {}", new_tz);
  log_handler_->info("Updated boot time: {}", 
                    std::chrono::system_clock::to_time_t(boot_time_));
}

std::string Statistics::get_system_timezone() {
  std::ifstream tz_file("/etc/timezone");
  std::string tz;
  if (tz_file) {
    std::getline(tz_file, tz);
  } else {
    // UTC-0 by default
    tz = "UTC";
  }
  return tz;
}

}  // namespace aby_box
