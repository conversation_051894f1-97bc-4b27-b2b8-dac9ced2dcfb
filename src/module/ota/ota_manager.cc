// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "module/ota/ota_manager.hpp"
#include "common/module_names.hpp"
#include "utils/api_client.h"
#include "module/cloud_sync_manager.hpp"
#include "module/wifi.hpp"
#include "utils/resource_manager.hpp"
#include "utils/safe_file_ops.hpp"
#include "utils/system_monitor.hpp"
#include <cstdlib>
#include <thread>
#include <chrono>
#include <curl/curl.h>
#include <fstream>
#include <filesystem>
#include <sys/stat.h>
#include <unistd.h>
#include <openssl/md5.h>
#include <iomanip>
#include <sstream>
#include <pthread.h>
#include <spdlog/spdlog.h>

namespace aby_box {

OtaManager::OtaManager(const std::string &module_name)
    : BaseModule(module_name), is_running_(false), is_upgrading_(false) {
}

OtaManager::~OtaManager() {
  stop();
  join();
}

bool OtaManager::init() {
  log_handler_->info("Initializing OtaManager module");

  // Register cleanup functions for OTA resources
  auto& resource_manager = ResourceManager::getInstance();
  resource_manager.registerCleanup("ota_temp_files", [this]() {
    cleanupTempFiles();
  });

  resource_manager.registerCleanup("ota_downloads", [this]() {
    cleanupDownloads();
  });

  return true;
}

bool OtaManager::start() {
  log_handler_->info("Starting OtaManager module");
  
  if (is_running_.load()) {
    log_handler_->warn("OtaManager already running");
    return true;
  }
  
  is_running_ = true;
  
  // Check system-level OTA cooldown on startup
  loadSystemCooldownState();
  
  log_handler_->info("InitializationManager ready, proceeding with OTA module startup");
  
  // Clean up expired retry records on start
  cleanupExpiredRetryRecords();
  
  return true;
}

bool OtaManager::stop() {
  log_handler_->info("Stopping OtaManager module");
  is_running_ = false;
  
  // If we're stopping and not in the middle of an upgrade, reset status to idle
  if (!is_upgrading_.load()) {
    updateOtaStatus("idle");
  }
  
  return true;
}

void OtaManager::join() {
  // Nothing to join as we removed the background thread
}

void OtaManager::registerModule(const std::string &module_name, 
                                std::shared_ptr<BaseModule> module) {
  std::lock_guard<std::mutex> lock(modules_mutex_);
  managed_modules_[module_name] = module;
  log_handler_->debug("Registered module for OTA management: {}", module_name);
}

bool OtaManager::startUpgrade(const std::string &target_version, const std::string &ota_url, const std::string &ota_md5_url) {
  if (is_upgrading_) {
    log_handler_->warn("Upgrade already in progress for version: {}", target_version_);
    return false;
  }

  // Store target version, OTA URL and MD5 URL for reference
  target_version_ = target_version;
  ota_url_ = ota_url;
  ota_md5_url_ = ota_md5_url;
  is_upgrading_ = true;

  log_handler_->info("Starting OTA upgrade to version: {}", target_version_.empty() ? "latest" : target_version_);

  // 启动系统监控
  OTA_START_MONITORING(target_version_.empty() ? "latest" : target_version_);
  if (!ota_url_.empty()) {
    log_handler_->info("Using OTA URL: {}", ota_url_);
  }
  if (!ota_md5_url_.empty()) {
    log_handler_->info("Using OTA MD5 URL: {}", ota_md5_url_);
  }
  
  // Update status to "updating" at the beginning of OTA
  updateOtaStatus("updating");
  
  // Create a new thread to run the upgrade process so we don't block
  std::thread upgrade_thread([this]() {
    log_handler_->info("Running OTA upgrade process for version: {}", target_version_.empty() ? "latest" : target_version_);

    try {
    
    // 清理/tmp/ota_update目录，确保每次OTA升级环境干净
    std::string tmp_dir = "/tmp/ota_update";
    std::error_code cleanup_ec;
    
    if (std::filesystem::exists(tmp_dir, cleanup_ec)) {
      log_handler_->info("Cleaning up existing OTA temporary directory: {}", tmp_dir);
      
      try {
        // 删除目录及其所有内容
        std::filesystem::remove_all(tmp_dir, cleanup_ec);
        if (cleanup_ec) {
          log_handler_->warn("Failed to remove existing OTA directory {}: {}", tmp_dir, cleanup_ec.message());
          // 尝试继续，但记录警告
        } else {
          log_handler_->info("Successfully cleaned up OTA temporary directory");
        }
      } catch (const std::exception& e) {
        log_handler_->warn("Exception while cleaning OTA directory {}: {}", tmp_dir, e.what());
        // 尝试继续，但记录警告
      }
    }
    
    // 重新创建干净的目录
    try {
      std::filesystem::create_directories(tmp_dir, cleanup_ec);
      if (cleanup_ec) {
        handleOtaError(OtaErrorType::FILESYSTEM_ERROR,
                      "Failed to create clean OTA directory",
                      cleanup_ec.message(), false);
        return;
      } else {
        log_handler_->info("Created clean OTA temporary directory: {}", tmp_dir);
      }
    } catch (const std::exception& e) {
      handleOtaError(OtaErrorType::FILESYSTEM_ERROR,
                    "Exception while creating OTA directory",
                    e.what(), false);
      return;
    }
    
    std::string ota_package_path;
    bool download_success = false;
    bool modules_stopped = false; // 标记是否已停止模块
    
    // First, download the OTA package if URL is provided
    if (!ota_url_.empty()) {
      log_handler_->info("Downloading OTA package from: {}", ota_url_);
      
      // Construct local file path
      std::string tmp_dir = "/tmp/ota_update";
      std::string zip_filename;
      
      // Extract filename from URL or construct default name
      size_t last_slash = ota_url_.find_last_of('/');
      size_t question_mark = ota_url_.find('?');
      
      if (last_slash != std::string::npos) {
        if (question_mark != std::string::npos && question_mark > last_slash) {
          zip_filename = ota_url_.substr(last_slash + 1, question_mark - last_slash - 1);
        } else {
          zip_filename = ota_url_.substr(last_slash + 1);
        }
      }
      
      // If we couldn't extract a proper filename, use default
      if (zip_filename.empty() || zip_filename.find('.') == std::string::npos) {
        if (!target_version_.empty()) {
          zip_filename = "core-image-minimal-aby-box-arm.rootfs-" + target_version_ + ".zip";
        } else {
          zip_filename = "ota_package.zip";
        }
      }
      
      ota_package_path = tmp_dir + "/" + zip_filename;
      // 构造MD5文件路径
      std::string md5_file_path = ota_package_path + ".md5";
      bool md5_download_success = false;
      
      // 如果提供了MD5 URL，先下载MD5文件
      if (!ota_md5_url_.empty()) {
        log_handler_->info("Downloading MD5 file from: {}", ota_md5_url_);
        md5_download_success = downloadFileWithRetry(ota_md5_url_, md5_file_path, 
                                                   "OTA MD5 file (" + target_version_ + ")");
        
        if (!md5_download_success) {
          handleOtaError(OtaErrorType::DOWNLOAD_ERROR,
                        "Failed to download MD5 file",
                        "URL: " + ota_md5_url_, true);
          return;
        } else {
          log_handler_->info("MD5 file downloaded successfully");
          OTA_UPDATE_PROGRESS(10.0, "MD5 file downloaded");
        }
      }
      
      // Download the OTA package
      log_handler_->info("Downloading OTA package from: {}", ota_url_);
      download_success = downloadFileWithRetry(ota_url_, ota_package_path, 
                                              "OTA package (" + target_version_ + ")");
      
      if (!download_success) {
        // 清理已下载的MD5文件
        if (md5_download_success) {
          std::filesystem::remove(md5_file_path);
        }

        handleOtaError(OtaErrorType::DOWNLOAD_ERROR,
                      "Failed to download OTA package",
                      "URL: " + ota_url_, true);
        return;
      }
      
      log_handler_->info("OTA package downloaded successfully");
      
      // 如果提供了MD5 URL，则必须进行MD5验证
      if (!ota_md5_url_.empty()) {
        log_handler_->info("Starting MD5 verification for downloaded OTA package");
        
        // 验证OTA包的MD5
        bool md5_verification_success = verifyFileMd5(ota_package_path, md5_file_path);
        
        // 清理MD5文件（不管验证是否成功都清理）
        std::filesystem::remove(md5_file_path);
        
        if (!md5_verification_success) {
          // 删除MD5验证失败的OTA包文件
          std::filesystem::remove(ota_package_path);

          handleOtaError(OtaErrorType::VERIFICATION_ERROR,
                        "MD5 verification failed for OTA package",
                        "Package deleted due to corruption", false);
          return;
        } else {
          log_handler_->info("MD5 verification passed for OTA package");
        }
      } else {
        log_handler_->warn("No MD5 URL provided, skipping MD5 verification");
      }
      
      log_handler_->info("OTA package downloaded and prepared successfully to: {}", ota_package_path);
    }
    
    // Verify we have a valid OTA package before stopping modules
    if (!download_success || ota_package_path.empty()) {
      handleOtaError(OtaErrorType::VERIFICATION_ERROR,
                    "No valid OTA package available for upgrade",
                    "Package validation failed", false);
      return;
    }
    
    // Now that we have a valid OTA package, stop all modules before executing the upgrade script
    log_handler_->info("OTA package ready, stopping all modules before executing upgrade script");
    stopAllModules();
    modules_stopped = true;
    
    // Execute the OTA upgrade script with environment variables
    std::string script_command = "/etc/tools/ota_upgrade.sh";
    
    // Set environment variables for the script
    if (!target_version_.empty()) {
      setenv("OTA_TARGET_VERSION", target_version_.c_str(), 1);
      log_handler_->debug("Set OTA_TARGET_VERSION environment variable: {}", target_version_);
    }
    
    // Pass the local package path
    setenv("OTA_PACKAGE_PATH", ota_package_path.c_str(), 1);
    log_handler_->debug("Set OTA_PACKAGE_PATH environment variable: {}", ota_package_path);
    
    log_handler_->info("Executing OTA upgrade script: {}", script_command);
    int result = std::system(script_command.c_str());
    
    // Clean up environment variables
    if (!target_version_.empty()) {
      unsetenv("OTA_TARGET_VERSION");
    }
    unsetenv("OTA_PACKAGE_PATH");
    
    log_handler_->info("OTA upgrade script completed with result code: {}", result);
    
    // Analyze the result and update status accordingly
    if (result == 0) {
      log_handler_->info("OTA upgrade script completed successfully for version: {}", target_version_);
      updateOtaStatus("completed");
      
      // 清除CloudSyncManager中的已处理版本记录，允许处理新版本
      if (cloud_sync_manager_) {
        cloud_sync_manager_->clearProcessedOtaVersion();
        log_handler_->info("Cleared processed OTA version record in CloudSyncManager");
      }
      
      // Script should handle reboot, but if we reach here without reboot, do it manually
      log_handler_->info("OTA completed successfully but system didn't reboot, triggering manual reboot");
      restartSystem();
    } else {
      // Determine error type based on exit code
      std::string error_reason;
      if (result == 256) {  // Common shell script error (exit 1)
        error_reason = "Script execution failed";
      } else if (result == 512) {  // Exit 2
        error_reason = "Download or file verification failed";
      } else if (result == 768) {  // Exit 3
        error_reason = "Partition preparation failed";
      } else {
        error_reason = "Unknown error (code: " + std::to_string(result) + ")";
      }
      
      log_handler_->error("OTA upgrade failed: {}", error_reason);
      updateOtaStatus("failed");
      
      // 在清空target_version_之前保存用于失败通知
      std::string failed_version = target_version_;
      
      // 通知CloudSyncManager记录失败版本，避免重复尝试
      log_handler_->debug("Checking CloudSyncManager notification conditions...");
      log_handler_->debug("cloud_sync_manager_ is {}", cloud_sync_manager_ ? "valid" : "null");
      log_handler_->debug("failed_version is: '{}'", failed_version);
      
      if (cloud_sync_manager_ && !failed_version.empty()) {
        log_handler_->info("Notifying CloudSyncManager about OTA failure for version: {}", failed_version);
        cloud_sync_manager_->notifyOtaFailure(failed_version);
        log_handler_->info("CloudSyncManager notified successfully about OTA failure for version: {}", failed_version);
      } else {
        if (!cloud_sync_manager_) {
          log_handler_->error("Cannot notify CloudSyncManager: pointer is null");
        }
        if (failed_version.empty()) {
          log_handler_->error("Cannot notify CloudSyncManager: failed_version is empty");
        }
      }
      
      // On failure, restart all modules (only if they were stopped) and reset upgrading state
      if (modules_stopped) {
        log_handler_->error("OTA script failed, triggering system restart");
        
        // 从当前target_version_获取失败版本
        std::string failed_version = target_version_;
        if (failed_version.empty()) {
          failed_version = "unknown_version";
        }
        
        // 记录系统级失败并重启
        recordSystemOtaFailureAndRestart(failed_version);
        return; // 函数将不会继续执行，因为系统会重启
      }
      is_upgrading_ = false;
      target_version_.clear();
      ota_url_.clear();
      ota_md5_url_.clear();
    }

    } catch (const std::exception& e) {
      handleOtaError(OtaErrorType::UNKNOWN_ERROR,
                    "Unexpected exception in OTA upgrade thread",
                    e.what(), false);
    } catch (...) {
      handleOtaError(OtaErrorType::UNKNOWN_ERROR,
                    "Unknown exception in OTA upgrade thread",
                    "", false);
    }
  });

  // Set thread name for better debugging
  pthread_setname_np(upgrade_thread.native_handle(), "aby_ota_upgrade");

  upgrade_thread.detach();
  return true;
}

bool OtaManager::restartSystem() {
  log_handler_->info("Restarting system...");
  
  // Sync filesystem to ensure all data is written
  std::system("sync");
  
  // Wait a moment for logs to be written
  std::this_thread::sleep_for(std::chrono::seconds(2));
  
  // Restart the system
  std::system("reboot -f");
  
  return true;
}

void OtaManager::stopAllModules() {
  log_handler_->info("Stopping all modules for OTA upgrade");
  
  std::lock_guard<std::mutex> lock(modules_mutex_);
  for (const auto &[name, module] : managed_modules_) {
    log_handler_->info("Stopping module: {}", name);
    module->stop();
  }
  
  // Give some time for modules to clean up
  std::this_thread::sleep_for(std::chrono::seconds(2));
  
  // Join all modules
  for (const auto &[name, module] : managed_modules_) {
    log_handler_->info("Joining module: {}", name);
    module->join();
  }
  
  log_handler_->info("All modules stopped for OTA upgrade");
}

void OtaManager::updateOtaStatus(const std::string &status) {
  log_handler_->debug("Updating OTA status to: {}", status);
  
  // Attempt to update status with retry logic
  const int max_retries = 3;
  const int retry_delay_ms = 500; // Reduced from 1000ms for faster updates during OTA
  
  for (int attempt = 1; attempt <= max_retries; ++attempt) {
    if (APIClient::getInstance().updateOtaStatus(status)) {
      log_handler_->debug("OTA status successfully updated to: {} (attempt {})", status, attempt);
      return;
    }
    
    if (attempt < max_retries) {
      log_handler_->warn("Failed to update OTA status to '{}' (attempt {}/{}), retrying in {}ms...", 
                        status, attempt, max_retries, retry_delay_ms);
      std::this_thread::sleep_for(std::chrono::milliseconds(retry_delay_ms));
    }
  }
  
  log_handler_->error("Failed to update OTA status to '{}' after {} attempts", status, max_retries);
}

// 静态回调函数：写入数据到文件
size_t OtaManager::writeCallback(void *contents, size_t size, size_t nmemb, void *userp) {
  auto* context = static_cast<DownloadContext*>(userp);
  
  // 检查是否被取消
  if (context->cancel_flag && *(context->cancel_flag)) {
    return 0; // 返回0会导致CURL停止传输
  }
  
  size_t realsize = size * nmemb;
  
  if (context->file && context->file->is_open()) {
    context->file->write(static_cast<const char*>(contents), realsize);
    context->file->flush(); // 确保数据写入磁盘
    
    if (context->file->fail()) {
      return 0; // 写入失败，停止下载
    }
  }
  
  return realsize;
}

// 静态回调函数：进度更新
size_t OtaManager::progressCallback(void *clientp, curl_off_t dltotal, curl_off_t dlnow, 
                                   curl_off_t ultotal, curl_off_t ulnow) {
  auto* context = static_cast<DownloadContext*>(clientp);
  
  // 检查是否被取消
  if (context->cancel_flag && *(context->cancel_flag)) {
    return 1; // 返回非0值会导致CURL停止传输
  }
  
  if (context->progress_callback) {
    // 严格的数值验证，防止异常数值
    const curl_off_t MAX_REASONABLE_SIZE = 10ULL * 1024 * 1024 * 1024; // 10GB
    
    // 检查数值合理性
    if (dltotal < 0 || dlnow < 0 || 
        dltotal > MAX_REASONABLE_SIZE || dlnow > MAX_REASONABLE_SIZE ||
        (dltotal > 0 && dlnow > dltotal)) {
      // 数值异常，记录调试信息但跳过进度回调
      static int debug_count = 0;
      if (debug_count < 5) { // 只记录前5次，避免日志洪水
        // 获取静态logger，因为这是静态函数
        spdlog::get("aby_box")->debug(
          "Skipping progress callback due to unreasonable CURL values: "
          "dltotal={}, dlnow={}, description={}", 
          dltotal, dlnow, context->description);
        debug_count++;
      }
      return 0;
    }
    
    size_t total_file_size = static_cast<size_t>(dltotal);
    size_t current_downloaded = static_cast<size_t>(dlnow);
    double percentage = 0.0;
    
    if (dltotal > 0) {
      percentage = (static_cast<double>(dlnow) / static_cast<double>(dltotal)) * 100.0;
      
      // 确保百分比在合理范围内
      if (percentage < 0.0) percentage = 0.0;
      if (percentage > 100.0) percentage = 100.0;
    }
    
    // 最终检查：确保数值合理才调用回调
    if (total_file_size > 0 && current_downloaded <= total_file_size) {
      context->progress_callback(current_downloaded, total_file_size, percentage);
    }
  }
  
  return 0; // 继续下载
}

// 直接下载文件（不支持断点续传）
bool OtaManager::downloadFile(const std::string &url, const std::string &local_path, 
                             const std::string &description, 
                             DownloadProgressCallback progress_callback) {
  CURL *curl;
  CURLcode res;
  
  curl = curl_easy_init();
  if (!curl) {
    log_handler_->error("Failed to initialize CURL for downloading {}", description);
    return false;
  }
  
  // 如果文件存在，先删除（每次都重新下载）
  if (std::filesystem::exists(local_path)) {
    std::error_code ec;
    std::filesystem::remove(local_path, ec);
    if (ec) {
      log_handler_->warn("Failed to remove existing file {}: {}", local_path, ec.message());
    } else {
      log_handler_->info("Removed existing file for fresh download: {}", local_path);
    }
  }
  
  log_handler_->info("Starting fresh download for {}", description);
  
  // 以二进制模式创建新文件
  std::ofstream file(local_path, std::ios::binary);
  if (!file.is_open()) {
    log_handler_->error("Failed to open file for writing: {}", local_path);
    curl_easy_cleanup(curl);
    return false;
  }
  
  // 设置下载上下文
  DownloadContext context;
  context.file = &file;
  context.progress_callback = progress_callback;
  context.description = description;
  context.resume_from = 0; // 不支持断点续传，始终从0开始
  context.cancel_flag = nullptr; // 可以后续扩展为支持取消
  
  // 配置CURL选项
  curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
  curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, writeCallback);
  curl_easy_setopt(curl, CURLOPT_WRITEDATA, &context);
  curl_easy_setopt(curl, CURLOPT_PROGRESSFUNCTION, progressCallback);
  curl_easy_setopt(curl, CURLOPT_PROGRESSDATA, &context);
  curl_easy_setopt(curl, CURLOPT_NOPROGRESS, 0L);
  
  // 网络配置
  curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);
  curl_easy_setopt(curl, CURLOPT_MAXREDIRS, 5L);
  curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 30L);
  curl_easy_setopt(curl, CURLOPT_TIMEOUT, 3600L); // 1小时超时
  curl_easy_setopt(curl, CURLOPT_LOW_SPEED_LIMIT, 1024L); // 1KB/s
  curl_easy_setopt(curl, CURLOPT_LOW_SPEED_TIME, 60L); // 60秒
  
  // SSL设置
  curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
  curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);
  
  // User Agent
  curl_easy_setopt(curl, CURLOPT_USERAGENT, "OTA-Downloader/1.0 (aby_box)");
  
  // 执行下载
  log_handler_->info("Starting download of {} from {}", description, url);
  
  res = curl_easy_perform(curl);
  
  // 获取响应信息
  long response_code;
  curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &response_code);
  
  double download_size;
  curl_easy_getinfo(curl, CURLINFO_SIZE_DOWNLOAD, &download_size);
  
  file.close();
  curl_easy_cleanup(curl);
  
  if (res != CURLE_OK) {
    log_handler_->error("Download failed for {}: {}", description, curl_easy_strerror(res));
    return false;
  }
  
  if (response_code >= 400) {
    log_handler_->error("Download failed for {}: HTTP error {}", description, response_code);
    return false;
  }
  
  // 验证下载的文件
  struct stat final_stat;
  if (stat(local_path.c_str(), &final_stat) != 0) {
    log_handler_->error("Downloaded file does not exist: {}", local_path);
    return false;
  }
  
  if (final_stat.st_size == 0) {
    log_handler_->error("Downloaded file is empty: {}", local_path);
    return false;
  }
  
  log_handler_->info("Download completed successfully: {} ({} bytes)", description, final_stat.st_size);
  return true;
}

// 主下载函数（带重试机制，不支持断点续传）
bool OtaManager::downloadFileWithRetry(const std::string &url, const std::string &local_path, 
                                      const std::string &description) {
  if (url.empty() || local_path.empty()) {
    log_handler_->error("Invalid URL or local path for download");
    return false;
  }
  
  const std::string desc = description.empty() ? "file" : description;
  const int max_immediate_retries = 3;
  const std::chrono::hours cooldown_period(24); // 24小时冷却期
  
  // 检查重试状态
  {
    std::lock_guard<std::mutex> lock(download_retry_mutex_);
    auto it = download_retry_map_.find(url);
    
    if (it != download_retry_map_.end() && it->second.in_cooldown) {
      auto now = std::chrono::system_clock::now();
      auto time_since_failure = now - it->second.last_failure_time;
      
      if (time_since_failure < cooldown_period) {
        auto remaining_time = cooldown_period - time_since_failure;
        auto remaining_hours = std::chrono::duration_cast<std::chrono::hours>(remaining_time).count();
        auto remaining_minutes = std::chrono::duration_cast<std::chrono::minutes>(remaining_time % std::chrono::hours(1)).count();
        
        log_handler_->warn("Download for {} is in cooldown period. Remaining time: {}h {}m", 
                          desc, remaining_hours, remaining_minutes);
        return false;
      } else {
        // 冷却期结束，重置状态
        log_handler_->info("Cooldown period ended for {}, resetting retry count", desc);
        it->second.consecutive_failures = 0;
        it->second.in_cooldown = false;
      }
    }
  }
  
  // 确保目标目录存在
  std::filesystem::path file_path(local_path);
  std::filesystem::path parent_dir = file_path.parent_path();
  
  std::error_code ec;
  if (!std::filesystem::exists(parent_dir, ec)) {
    std::filesystem::create_directories(parent_dir, ec);
    if (ec) {
      log_handler_->error("Failed to create directory {}: {}", parent_dir.string(), ec.message());
      return false;
    }
  }
  
  // 执行立即重试
  for (int attempt = 1; attempt <= max_immediate_retries; ++attempt) {
    log_handler_->info("Download attempt {}/{} for {}", attempt, max_immediate_retries, desc);
    
    // 直接下载到最终文件路径（每次重新下载，不使用进度回调）
    if (downloadFile(url, local_path, desc, nullptr)) {
      log_handler_->info("Download successful for {} on attempt {}", desc, attempt);
      
      // 成功后清除重试记录
      {
        std::lock_guard<std::mutex> lock(download_retry_mutex_);
        download_retry_map_.erase(url);
      }
      
      return true;
    }
    
    if (attempt < max_immediate_retries) {
      int delay_minutes = 3; // 统一改为3分钟间隔
      log_handler_->warn("Download attempt {}/{} failed for {}, retrying in {} minutes...", 
                        attempt, max_immediate_retries, desc, delay_minutes);
      std::this_thread::sleep_for(std::chrono::minutes(delay_minutes));
    }
  }
  
  // 所有立即重试都失败了，进入冷却期
  {
    std::lock_guard<std::mutex> lock(download_retry_mutex_);
    auto& retry_info = download_retry_map_[url];
    retry_info.url = url;
    retry_info.last_failure_time = std::chrono::system_clock::now();
    retry_info.consecutive_failures++;
    retry_info.in_cooldown = true;
    
    log_handler_->error("Download failed for {} after {} attempts (3 minutes interval each). " 
                       "Entering 24-hour cooldown period.", 
                       desc, max_immediate_retries);
    
    // OTA下载失败只进入24小时冷却期，不进行系统重启
    bool is_ota_download = (desc.find("OTA") != std::string::npos || 
                           desc.find("ota") != std::string::npos || 
                           is_upgrading_.load());
    
    if (is_ota_download) {
      log_handler_->warn("OTA download failed after 3 attempts. Entering 24-hour cooldown period.");
      log_handler_->info("System will continue running normally during cooldown period.");
    }
  }
  
  return false;
}

// 清理过期的重试记录
void OtaManager::cleanupExpiredRetryRecords() {
  std::lock_guard<std::mutex> lock(download_retry_mutex_);
  
  auto now = std::chrono::system_clock::now();
  const std::chrono::hours max_record_age(7 * 24); // 保留7天的记录
  
  auto it = download_retry_map_.begin();
  while (it != download_retry_map_.end()) {
    auto time_since_failure = now - it->second.last_failure_time;
    
    if (time_since_failure > max_record_age) {
      log_handler_->debug("Cleaning up expired retry record for URL: {}", it->first);
      it = download_retry_map_.erase(it);
    } else {
      ++it;
    }
  }
}

// 计算文件的MD5值 - 使用RAII资源管理
std::string OtaManager::calculateFileMd5(const std::string &file_path) {
  try {
    SCOPED_FILE(file, file_path, std::ios::binary);
    if (!file.is_open()) {
      log_handler_->error("Failed to open file for MD5 calculation: {}", file_path);
      return "";
    }

    MD5_CTX md5Context;
    MD5_Init(&md5Context);

    // Use RAII memory management for buffer
    SCOPED_MEMORY(char, buffer, 8192);

    while (file.get().read(buffer.get(), buffer.size()) || file.get().gcount() > 0) {
      MD5_Update(&md5Context, buffer.get(), file.get().gcount());
    }

    unsigned char result[MD5_DIGEST_LENGTH];
    MD5_Final(result, &md5Context);

    // 转换为十六进制字符串
    std::ostringstream oss;
    for (int i = 0; i < MD5_DIGEST_LENGTH; ++i) {
      oss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(result[i]);
    }

    return oss.str();

  } catch (const std::exception& e) {
    log_handler_->error("Exception during MD5 calculation for {}: {}", file_path, e.what());
    return "";
  }
}

// 验证文件MD5 - 使用安全文件操作
bool OtaManager::verifyFileMd5(const std::string &file_path, const std::string &md5_file_path) {
  auto& safe_ops = getSafeFileOps();

  // 安全读取MD5文件内容
  auto md5_content = safe_ops.readFileContent(md5_file_path);
  if (!md5_content) {
    log_handler_->error("Failed to read MD5 file: {}", md5_file_path);
    return false;
  }

  std::string expected_md5 = *md5_content;
  
  // 清理MD5字符串（移除空白字符和可能的文件名）
  size_t space_pos = expected_md5.find(' ');
  if (space_pos != std::string::npos) {
    expected_md5 = expected_md5.substr(0, space_pos);
  }
  
  // 移除首尾空白字符
  expected_md5.erase(0, expected_md5.find_first_not_of(" \t\r\n"));
  expected_md5.erase(expected_md5.find_last_not_of(" \t\r\n") + 1);
  
  if (expected_md5.empty()) {
    log_handler_->error("MD5 file is empty or invalid: {}", md5_file_path);
    return false;
  }
  
  // 计算文件的实际MD5
  std::string actual_md5 = calculateFileMd5(file_path);
  if (actual_md5.empty()) {
    log_handler_->error("Failed to calculate MD5 for file: {}", file_path);
    return false;
  }
  
  // 比较MD5值（不区分大小写）
  std::transform(expected_md5.begin(), expected_md5.end(), expected_md5.begin(), ::tolower);
  std::transform(actual_md5.begin(), actual_md5.end(), actual_md5.begin(), ::tolower);
  
  if (expected_md5 == actual_md5) {
    log_handler_->info("MD5 verification passed for file: {}", file_path);
    log_handler_->debug("Expected MD5: {}", expected_md5);
    log_handler_->debug("Actual MD5: {}", actual_md5);
    return true;
  } else {
    log_handler_->error("MD5 verification failed for file: {}", file_path);
    log_handler_->error("Expected MD5: {}", expected_md5);
    log_handler_->error("Actual MD5: {}", actual_md5);
    return false;
  }
}

// System-level OTA cooldown management methods - 使用安全文件操作
bool OtaManager::isInSystemOtaCooldown() const {
  auto& safe_ops = getSafeFileOps();

  auto content = safe_ops.readFileContent(SYSTEM_COOLDOWN_FILE);
  if (!content) {
    return false; // 文件不存在，不在冷却期
  }

  std::string line = *content;
  
  if (line.empty()) {
    return false;
  }
  
  try {
    // 解析时间戳
    std::time_t failure_time = std::stoll(line);
    auto failure_timepoint = std::chrono::system_clock::from_time_t(failure_time);
    auto now = std::chrono::system_clock::now();
    auto elapsed = now - failure_timepoint;
    
    if (elapsed < SYSTEM_COOLDOWN_PERIOD) {
      auto remaining_time = SYSTEM_COOLDOWN_PERIOD - elapsed;
      auto remaining_hours = std::chrono::duration_cast<std::chrono::hours>(remaining_time).count();
      auto remaining_minutes = std::chrono::duration_cast<std::chrono::minutes>(remaining_time % std::chrono::hours(1)).count();
      
      log_handler_->warn("System is in OTA cooldown period. Remaining time: {}h {}m", 
                        remaining_hours, remaining_minutes);
      return true;
    } else {
      // 冷却期已过，删除冷却状态文件
      log_handler_->info("System OTA cooldown period has expired, clearing cooldown state");
      removeSystemCooldownState();
      return false;
    }
  } catch (const std::exception &e) {
    log_handler_->error("Failed to parse system cooldown state: {}", e.what());
    removeSystemCooldownState(); // 删除损坏的文件
    return false;
  }
}

void OtaManager::recordSystemOtaFailureAndRestart(const std::string &failed_version) {
  log_handler_->warn("Recording system-level OTA failure for version: {}", failed_version);
  log_handler_->warn("System will be restarted and OTA will be blocked for 24 hours");
  
  // 保存失败状态到持久化文件
  saveSystemCooldownState(failed_version);
  
  // 通知CloudSyncManager记录失败版本
  if (cloud_sync_manager_ && !failed_version.empty()) {
    log_handler_->info("Notifying CloudSyncManager about system-level OTA failure for version: {}", failed_version);
    cloud_sync_manager_->notifyOtaFailure(failed_version);
  }
  
  // 更新OTA状态为失败
  updateOtaStatus("failed");
  
  // 等待状态更新完成
  std::this_thread::sleep_for(std::chrono::seconds(3));
  
  // 重启系统
  log_handler_->warn("Initiating system restart after OTA failure...");
  restartSystem();
}

void OtaManager::clearSystemOtaCooldown() {
  removeSystemCooldownState();
  log_handler_->info("System OTA cooldown has been cleared");
}

bool OtaManager::loadSystemCooldownState() {
  if (isInSystemOtaCooldown()) {
    log_handler_->warn("System is currently in OTA cooldown period");
    return true;
  }
  return false;
}

void OtaManager::saveSystemCooldownState(const std::string &failed_version) {
  auto& safe_ops = getSafeFileOps();

  // 确保目录存在
  std::filesystem::path cooldown_path(SYSTEM_COOLDOWN_FILE);
  std::filesystem::path parent_dir = cooldown_path.parent_path();

  auto dir_result = safe_ops.createDirectories(parent_dir.string());
  if (dir_result != FileOpResult::SUCCESS) {
    log_handler_->error("Failed to create directory for cooldown file: {}",
                       SafeFileOps::getErrorString(dir_result));
    return;
  }

  // 使用安全文件操作写入冷却状态
  auto now = std::chrono::system_clock::now();
  auto timestamp = std::chrono::system_clock::to_time_t(now);
  std::string content = std::to_string(timestamp) + "\n" + failed_version;

  auto result = safe_ops.writeFileAtomic(SYSTEM_COOLDOWN_FILE, content);
  if (result != FileOpResult::SUCCESS) {
    log_handler_->error("Failed to save system cooldown state: {}",
                       SafeFileOps::getErrorString(result));
    return;
  }
  
  // 写入当前时间戳
  auto now = std::chrono::system_clock::now();
  auto timestamp = std::chrono::system_clock::to_time_t(now);
  cooldown_file << timestamp << std::endl;
  cooldown_file << failed_version << std::endl;
  cooldown_file.close();
  
  log_handler_->info("System cooldown state saved to: {}", SYSTEM_COOLDOWN_FILE);
}

void OtaManager::removeSystemCooldownState() {
  std::error_code ec;
  if (std::filesystem::exists(SYSTEM_COOLDOWN_FILE, ec)) {
    std::filesystem::remove(SYSTEM_COOLDOWN_FILE, ec);
    if (!ec) {
      log_handler_->info("System cooldown state file removed");
    } else {
      log_handler_->error("Failed to remove system cooldown state file: {}", ec.message());
    }
  }
}

// Enhanced error handling methods
void OtaManager::handleOtaError(OtaErrorType type, const std::string& message,
                               const std::string& details, bool is_recoverable) {
  std::lock_guard<std::mutex> lock(error_mutex_);

  // Create error info
  OtaErrorInfo error;
  error.type = type;
  error.message = message;
  error.details = details;
  error.timestamp = std::chrono::system_clock::now();
  error.retry_count = 0;

  // Log the error
  logOtaError(error);

  // Add to error history
  error_history_.push_back(error);
  if (error_history_.size() > MAX_ERROR_HISTORY) {
    error_history_.erase(error_history_.begin());
  }

  // Update OTA status
  updateOtaStatus("failed");

  // Attempt recovery if the error is recoverable
  if (is_recoverable && isErrorRecoverable(type)) {
    log_handler_->info("Attempting error recovery for recoverable error");
    if (attemptErrorRecovery(error)) {
      log_handler_->info("Error recovery successful");
      return;
    } else {
      log_handler_->error("Error recovery failed");
    }
  }

  // If not recoverable or recovery failed, reset OTA state
  resetOtaState(true);

  // Notify CloudSyncManager about the failure
  std::string failed_version = target_version_;
  if (failed_version.empty()) {
    failed_version = "error_" + std::to_string(static_cast<int>(type));
  }

  if (cloud_sync_manager_ && !failed_version.empty()) {
    log_handler_->info("Notifying CloudSyncManager about OTA error for version: {}", failed_version);
    cloud_sync_manager_->notifyOtaFailure(failed_version);
  }

  // For critical errors, trigger system restart
  if (!is_recoverable || type == OtaErrorType::SYSTEM_ERROR) {
    log_handler_->error("Critical OTA error occurred, triggering system restart");
    recordSystemOtaFailureAndRestart(failed_version);
  }
}

bool OtaManager::attemptErrorRecovery(const OtaErrorInfo& error) {
  log_handler_->info("Attempting recovery for error type: {}", static_cast<int>(error.type));

  switch (error.type) {
    case OtaErrorType::NETWORK_ERROR:
      // Wait for network to stabilize
      log_handler_->info("Network error detected, waiting for network stabilization");
      std::this_thread::sleep_for(std::chrono::seconds(30));

      // Check if network is available
      try {
        auto& wifi = Wifi::getInstance();
        if (wifi.isConnected() && wifi.isNetworkStable()) {
          log_handler_->info("Network recovery successful");
          return true;
        }
      } catch (const std::exception& e) {
        log_handler_->error("Failed to check network status during recovery: {}", e.what());
      }
      return false;

    case OtaErrorType::DOWNLOAD_ERROR:
      // Clear download retry state and attempt immediate retry
      log_handler_->info("Download error detected, clearing retry state");
      {
        std::lock_guard<std::mutex> lock(download_retry_mutex_);
        download_retry_map_.clear();
      }
      return false; // Let the normal retry mechanism handle this

    case OtaErrorType::FILESYSTEM_ERROR:
      // Try to clean up and recreate directories
      log_handler_->info("Filesystem error detected, attempting cleanup");
      try {
        std::string tmp_dir = "/tmp/ota_update";
        std::error_code ec;

        if (std::filesystem::exists(tmp_dir, ec)) {
          std::filesystem::remove_all(tmp_dir, ec);
        }

        std::filesystem::create_directories(tmp_dir, ec);
        if (!ec) {
          log_handler_->info("Filesystem recovery successful");
          return true;
        }
      } catch (const std::exception& e) {
        log_handler_->error("Filesystem recovery failed: {}", e.what());
      }
      return false;

    default:
      log_handler_->warn("No recovery strategy available for error type: {}", static_cast<int>(error.type));
      return false;
  }
}

void OtaManager::logOtaError(const OtaErrorInfo& error) {
  std::string error_type_str;
  switch (error.type) {
    case OtaErrorType::NETWORK_ERROR: error_type_str = "NETWORK_ERROR"; break;
    case OtaErrorType::DOWNLOAD_ERROR: error_type_str = "DOWNLOAD_ERROR"; break;
    case OtaErrorType::VERIFICATION_ERROR: error_type_str = "VERIFICATION_ERROR"; break;
    case OtaErrorType::FILESYSTEM_ERROR: error_type_str = "FILESYSTEM_ERROR"; break;
    case OtaErrorType::SCRIPT_ERROR: error_type_str = "SCRIPT_ERROR"; break;
    case OtaErrorType::SYSTEM_ERROR: error_type_str = "SYSTEM_ERROR"; break;
    default: error_type_str = "UNKNOWN_ERROR"; break;
  }

  log_handler_->error("OTA Error [{}]: {}", error_type_str, error.message);
  if (!error.details.empty()) {
    log_handler_->error("OTA Error Details: {}", error.details);
  }

  // Also log to system log for debugging
  spdlog::error("OTA Error [{}]: {} - {}", error_type_str, error.message, error.details);
}

bool OtaManager::isErrorRecoverable(OtaErrorType type) const {
  switch (type) {
    case OtaErrorType::NETWORK_ERROR:
    case OtaErrorType::DOWNLOAD_ERROR:
    case OtaErrorType::FILESYSTEM_ERROR:
      return true;
    case OtaErrorType::VERIFICATION_ERROR:
    case OtaErrorType::SCRIPT_ERROR:
    case OtaErrorType::SYSTEM_ERROR:
    case OtaErrorType::UNKNOWN_ERROR:
    default:
      return false;
  }
}

void OtaManager::resetOtaState(bool preserve_error_info) {
  log_handler_->info("Resetting OTA state (preserve_error_info: {})", preserve_error_info);

  is_upgrading_ = false;
  target_version_.clear();
  ota_url_.clear();
  ota_md5_url_.clear();

  if (!preserve_error_info) {
    std::lock_guard<std::mutex> lock(error_mutex_);
    error_history_.clear();
  }

  // Reset error state in base module
  reset_error_state();

  log_handler_->info("OTA state reset completed");
}

// Resource cleanup methods
void OtaManager::cleanupTempFiles() {
  log_handler_->info("Cleaning up OTA temporary files");

  try {
    std::string tmp_dir = "/tmp/ota_update";
    if (std::filesystem::exists(tmp_dir)) {
      std::error_code ec;
      auto removed = std::filesystem::remove_all(tmp_dir, ec);
      if (ec) {
        log_handler_->warn("Failed to remove OTA temp directory: {}", ec.message());
      } else {
        log_handler_->info("Removed {} files from OTA temp directory", removed);
      }
    }
  } catch (const std::exception& e) {
    log_handler_->error("Exception during temp file cleanup: {}", e.what());
  }
}

void OtaManager::cleanupDownloads() {
  log_handler_->info("Cleaning up OTA download files");

  try {
    // Clear download retry state
    {
      std::lock_guard<std::mutex> lock(download_retry_mutex_);
      download_retry_map_.clear();
    }

    // Remove old download files
    std::vector<std::string> patterns = {
      "/tmp/ota_update/*.tar.gz",
      "/tmp/ota_update/*.md5",
      "/tmp/ota_update/*.tmp"
    };

    for (const auto& pattern : patterns) {
      std::string cmd = "rm -f " + pattern + " 2>/dev/null || true";
      system(cmd.c_str());
    }

    log_handler_->info("OTA download cleanup completed");

  } catch (const std::exception& e) {
    log_handler_->error("Exception during download cleanup: {}", e.what());
  }
}

} // namespace aby_box