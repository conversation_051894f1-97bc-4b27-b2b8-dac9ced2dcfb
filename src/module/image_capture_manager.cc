#include "module/image_capture_manager.hpp"
#include "module/video_engine/mpi/image_capture_queue.h"
#include "utils/logging.hpp"
#include "utils/api_client.h"
#include <filesystem>
#include <algorithm>
#include <ctime>
#include <iomanip>
#include <sstream>

// 使用完整版的stb_image_write库
#include "stb_image_write.h"
// 使用stb_image_resize2进行高质量图像缩放
#define STB_IMAGE_RESIZE_IMPLEMENTATION
#include "stb_image_resize2.h"

namespace aby_box {

ImageCaptureManager::ImageCaptureManager(const std::string &module_name)
    : BaseModule(module_name) {}

ImageCaptureManager::~ImageCaptureManager() {
    stop();
    join();
}

bool ImageCaptureManager::init() {
    log_handler_->info("ImageCaptureManager initialized");
    return true;
}

bool ImageCaptureManager::start() {
    if (is_running_) {
        return true;
    }
    
    is_running_ = true;
    event_thread_ = std::make_unique<std::thread>(&ImageCaptureManager::event_subscriber_loop, this);
    pthread_setname_np(event_thread_->native_handle(), "aby_img_capture");
    
    log_handler_->info("ImageCaptureManager started");
    return true;
}

bool ImageCaptureManager::stop() {
    is_running_ = false;
    is_capturing_ = false;
    return true;
}

void ImageCaptureManager::join() {
    if (event_thread_ && event_thread_->joinable()) {
        event_thread_->join();
    }
}

void ImageCaptureManager::event_subscriber_loop() {
    uorb::SubscriptionData<uorb::msg::cat_event> sub_cat_event;
    int timeout_ms = 100;

    struct orb_pollfd poll_fds[] = {
        {.fd = sub_cat_event.handle(), .events = POLLIN, .revents = 0}
    };

    while (is_running_) {
        if (0 < orb_poll(poll_fds, ARRAY_SIZE(poll_fds), timeout_ms)) {
            if (poll_fds[0].revents & POLLIN && sub_cat_event.Update()) {
                auto data = sub_cat_event.get();
                
                if (data.event_type == CAT_ENTER) {
                    log_handler_->info("Cat entered - starting image capture");
                    // 不再存储时间戳，改为从APIClient获取统一时间戳
                    is_capturing_ = true;
                    
                    // 清空之前的图像
                    std::lock_guard<std::mutex> lock(images_mutex_);
                    captured_images_.clear();
                    
                } else if (data.event_type == CAT_LEAVE) {
                    log_handler_->info("Cat left - stopping image capture and saving top images");
                    is_capturing_ = false;
                    save_top_images();
                    
                    // 清空置信度数据，为下一次猫入盆任务做准备
                    ImageCaptureQueue::getInstance().clearConfidenceData();
                }
            }
        }
    }
}

void ImageCaptureManager::capture_frame(const uint8_t* frame_data, uint32_t width, 
                                       uint32_t height, float confidence, uint64_t timestamp) {
    // 检查是否正在捕获 && 置信度足够
    if (!is_capturing_ || confidence < MIN_CONFIDENCE) {
        return;
    }

    // 性能优化：限制捕获频率，避免连续捕获相同的帧
    static uint64_t last_capture_time = 0;
    static constexpr uint64_t MIN_CAPTURE_INTERVAL_US = 500000; // 500ms
    
    if (timestamp - last_capture_time < MIN_CAPTURE_INTERVAL_US) {
        return;
    }
    last_capture_time = timestamp;

    std::lock_guard<std::mutex> lock(images_mutex_);

    // 创建CapturedImage对象
    size_t data_size = width * height * 3; // RGB
    std::vector<uint8_t> image_data(data_size);
    std::memcpy(image_data.data(), frame_data, data_size);

    auto captured_image = std::make_unique<CapturedImage>(
        std::move(image_data), width, height, 3, confidence, timestamp);

    // 直接添加到vector中
    captured_images_.push_back(std::move(captured_image));

    // 按置信度排序，保持最高的在前面
    std::sort(captured_images_.begin(), captured_images_.end(),
              [](const auto& a, const auto& b) {
                  return a->confidence > b->confidence;
              });

    // 如果超过最大数量，移除置信度最低的
    if (captured_images_.size() > MAX_IMAGES) {
        captured_images_.resize(MAX_IMAGES);
    }
    
    log_handler_->debug("Captured image with confidence: {:.3f}, total images: {}",
                       confidence, captured_images_.size());
}

void ImageCaptureManager::save_top_images() {
    std::lock_guard<std::mutex> lock(images_mutex_);
    
    if (captured_images_.empty()) {
        log_handler_->warn("No images to save");
        return;
    }

    // 获取保存目录 - 从APIClient获取统一的录制时间戳
    uint64_t recording_timestamp = aby_box::APIClient::getInstance().getCurrentRecordingTimestamp();
    if (recording_timestamp == 0) {
        log_handler_->error("No valid recording timestamp available, cannot save images");
        return;
    }
    std::string save_dir = get_save_directory(recording_timestamp);
    
    // 创建目录
    try {
        std::filesystem::create_directories(save_dir);
    } catch (const std::exception& e) {
        log_handler_->error("Failed to create directory {}: {}", save_dir, e.what());
        return;
    }

    // 图像已经按置信度排序，直接取前三个
    // cover.jpg (最高置信度) -> cover_bak0.jpg -> cover_bak1.jpg (最低置信度)
    const std::vector<std::string> filenames = {"cover.jpg", "cover_bak0.jpg", "cover_bak1.jpg"};

    size_t saved_count = 0;
    size_t images_to_save = std::min(captured_images_.size(), TOP_IMAGES_COUNT);

    for (size_t i = 0; i < images_to_save; ++i) {
        const auto& image = *captured_images_[i];

        std::string filename = save_dir + "/" + filenames[i];

        if (save_image_to_file(image, filename)) {
            saved_count++;
            log_handler_->info("Saved image {}/{}: {} (confidence: {:.3f})",
                              i + 1, TOP_IMAGES_COUNT, filename, image.confidence);
        }
    }
    
    log_handler_->info("Successfully saved {}/{} top confidence images", 
                      saved_count, TOP_IMAGES_COUNT);
    
    // 清空图像缓存
    captured_images_.clear();
}

std::string ImageCaptureManager::get_save_directory(uint64_t timestamp) {
    std::time_t ts = timestamp;
    std::tm tm_info = *std::localtime(&ts);
    
    std::ostringstream oss;
    oss << "/mnt/recordings/" 
        << std::put_time(&tm_info, "%Y-%m-%d_%H-%M-%S") 
        << "_hls";
    
    return oss.str();
}

bool ImageCaptureManager::save_image_to_file(const CapturedImage& image, const std::string& filename) {
    // 压缩配置：针对1280x720优化为640x360
    const int jpeg_quality = 80; // 进一步降低质量以减小文件体积
    const uint32_t target_width = 640;  // 目标宽度：640像素
    const uint32_t target_height = 360; // 目标高度：360像素
    
    // 对于1280x720的图片，总是缩放到640x360
    bool need_resize = (image.width != target_width || image.height != target_height);
    
    if (need_resize) {
        // 使用stb_image_resize2进行高质量缩放
        std::vector<uint8_t> resized_data(target_width * target_height * image.channels);
        
        // 使用stb_image_resize2进行高质量缩放
        int resize_result = stbir_resize_uint8_linear(
            image.data.data(),                    // input pixels
            static_cast<int>(image.width),        // input width
            static_cast<int>(image.height),       // input height
            0,                                    // input stride (0 = tightly packed)
            resized_data.data(),                  // output pixels
            static_cast<int>(target_width),       // output width
            static_cast<int>(target_height),      // output height
            0,                                    // output stride (0 = tightly packed)
            image.channels == 3 ? STBIR_RGB : STBIR_RGBA // pixel layout
        );
        
        if (resize_result == 0) {
            log_handler_->error("Failed to resize image from {}x{} to {}x{}", 
                               image.width, image.height, target_width, target_height);
            return false;
        }
        
        // 保存缩放后的图像
        int result = stbi_write_jpg(filename.c_str(), 
                                   static_cast<int>(target_width),
                                   static_cast<int>(target_height),
                                   static_cast<int>(image.channels),
                                   resized_data.data(),
                                   jpeg_quality);
        
        if (result == 0) {
            log_handler_->error("Failed to save resized image: {}", filename);
            return false;
        }
        
        log_handler_->debug("Saved compressed image: {}x{} -> {}x{}, quality: {}", 
                           image.width, image.height, target_width, target_height, jpeg_quality);
    }
    
    return true;
}



} // namespace aby_box 