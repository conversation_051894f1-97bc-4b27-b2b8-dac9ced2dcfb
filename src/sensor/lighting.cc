// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "sensor/lighting.hpp"
#include "common/topics/sensor_light.hpp"
#include "uorb/publication.h"
#include "uorb/abs_time.h"
#include <filesystem>
#include <thread>

namespace aby_box {

Lighting::Lighting(const std::string &module_name)
    : BaseModule(module_name), is_running_(false) {}

Lighting::~Lighting() {
  if (is_running_) {
    stop();
  }
  join();
}

bool Lighting::init() {
  fd_ = open("/dev/ltr303", O_RDWR);
  if (fd_ < 0) {
    log_handler_->error("Failed to open LTR303 device");
    return false;
  }

  // 初始化传感器
  if (ioctl(fd_, LTR303_IOC_INIT_SENSOR) < 0) {
    log_handler_->error("Failed to initialize LTR303 sensor");
    close(fd_);
    fd_ = -1;
    return false;
  }

  return true;
}

bool Lighting::start() {
  is_running_ = true;
  thread_publisher_ = std::thread(&Lighting::thread_publisher, this);
  // 设置线程名称
  pthread_setname_np(thread_publisher_.native_handle(), "aby_lighting");
  return true;
}

bool Lighting::stop() {
  is_running_ = false;
  return true;
}

void Lighting::join() {
  if (is_running_) {
    stop();
  }
  if (thread_publisher_.joinable()) {
    thread_publisher_.join();
  }
  if (fd_ > 0) {
    close(fd_);
    fd_ = -1;
  }
}

void Lighting::thread_publisher() {
  uorb::PublicationData<uorb::msg::sensor_light> pub_sensor_light;
  struct ltr303_data data;
  
  while (!is_running_) {
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
  }
  
  while (is_running_) {
    // 读取传感器数据
    if (ioctl(fd_, LTR303_IOC_READ_DATA, &data) < 0) {
      log_handler_->error("Failed to read LTR303 data");
      std::this_thread::sleep_for(std::chrono::milliseconds(200));
      continue;
    }
    
    auto &msg = pub_sensor_light.get();
    msg.timestamp = orb_absolute_time_us();
    msg.device_id = 0;
    msg.light_visible = data.ch0;
    msg.light_ir = data.ch1;
    
    if (!pub_sensor_light.Publish()) {
      log_handler_->error("Failed to publish light data");
    }

    // log_handler_->info("Lighting: visible={}, ir={}", data.ch0, data.ch1);
    
    std::this_thread::sleep_for(std::chrono::milliseconds(200)); // 200ms采样间隔
  }
}

} // namespace aby_box