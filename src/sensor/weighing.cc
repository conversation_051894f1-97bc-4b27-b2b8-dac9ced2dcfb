// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "sensor/weighing.hpp"
#include <filesystem>
#include <json-c/json.h>
#include <thread>
#include <algorithm>
#include <numeric>

namespace aby_box {

Weighing::Weighing(const std::string &module_name, bool enable_debug, bool calibration_mode)
    : BaseModule(module_name), calibration_mode_(calibration_mode) {
    // Initialize weight filter
    weight_filter_ = std::make_unique<WeightFilter>();
    weight_filter_->windowSize = 5;
    weight_filter_->currentSize = 0;
    weight_filter_->spikeThreshold = 0.015; // 1.5%
    weight_filter_->stableThreshold = 0.001; // 0.1%
    weight_filter_->minStableCount = 3;
    weight_filter_->isFirstFrame = true;
    weight_filter_->lastValidValue = 0.0;
    weight_filter_->consecutiveChanges = 0;
    weight_filter_->lastChange = 0.0;
    weight_filter_->window.resize(weight_filter_->windowSize);
    
    // ... rest of existing initialization ...
}

Weighing::~Weighing() {
    if (is_running_) {
        stop();
    }
    join();
}

bool Weighing::init() {
    fd_ = open("/dev/hx711", O_RDWR);
    if (fd_ < 0) {
        log_handler_->error("Failed to open HX711 device");
        return false;
    }

    if (!calibration_mode_) {
        if (!load_calibration()) {
            log_handler_->error("Failed to load calibration data");
            return false;
        }
    }
    return true;
}

bool Weighing::start() {
    is_running_ = true;
    thread_publisher_ = std::thread(&Weighing::thread_publisher, this);
    // 设置线程名称
    pthread_setname_np(thread_publisher_.native_handle(), "aby_weighing");
    return true;
}

bool Weighing::stop() {
    is_running_ = false;
    return true;
}

void Weighing::join() {
    if (is_running_) {
        stop();
    }
    if (thread_publisher_.joinable()) {
        thread_publisher_.join();
    }
    if (fd_ > 0) {
        close(fd_);
        fd_ = -1;
    }
}

double Weighing::calculate_mean(const std::vector<double>& data) {
    double sum = 0.0;
    for (const auto& value : data) {
        sum += value;
    }
    return sum / data.size();
}

bool Weighing::is_data_stable(const WeightFilter* filter, double mean) {
    size_t stableCount = 0;
    for (size_t i = 0; i < filter->currentSize; i++) {
        if (std::abs(filter->window[i] - mean) / mean <= filter->stableThreshold) {
            stableCount++;
        }
    }
    return stableCount >= filter->minStableCount;
}

void Weighing::add_to_window(WeightFilter* filter, double value) {
    if (filter->currentSize < filter->windowSize) {
        filter->window[filter->currentSize++] = value;
    } else {
        // Move window
        std::rotate(filter->window.begin(), filter->window.begin() + 1, filter->window.end());
        filter->window[filter->windowSize - 1] = value;
    }
}

double Weighing::filter_weight_signal(double raw_weight) {
    // Handle first frame
    if (weight_filter_->isFirstFrame) {
        weight_filter_->isFirstFrame = false;
        weight_filter_->lastValidValue = raw_weight;
        return raw_weight;
    }
    
    // Check for changes in specific ranges
    double weightDiff = raw_weight - weight_filter_->lastValidValue;
    if ((weightDiff > 10.0 && weightDiff < 100.0) ||    // Positive change range
        (weightDiff < -10.0 && weightDiff > -100.0)) {  // Negative change range
        // Keep last valid value for changes in these ranges
        // log_handler_->debug("Change in filter range: {:.2f}, keeping last value: {:.2f}", 
        //                   weightDiff, weight_filter_->lastValidValue);
        return weight_filter_->lastValidValue;
    }
    
    // Handle first valid value
    if (weight_filter_->currentSize == 0) {
        add_to_window(weight_filter_.get(), raw_weight);
        weight_filter_->lastValidValue = raw_weight;
        weight_filter_->consecutiveChanges = 0;
        weight_filter_->lastChange = 0.0;
        return raw_weight;
    }
    
    // Calculate current window mean
    double mean = calculate_mean(weight_filter_->window);
    
    // Calculate relative change
    double relativeChange = (raw_weight - mean) / mean;
    double absChange = std::abs(relativeChange);
    
    // Check for spike
    if (absChange > weight_filter_->spikeThreshold) {
        // Check for consistent trend
        if (weight_filter_->consecutiveChanges > 0) {
            if ((relativeChange > 0 && weight_filter_->lastChange > 0) ||
                (relativeChange < 0 && weight_filter_->lastChange < 0)) {
                weight_filter_->consecutiveChanges++;
                
                // Accept value after 3 consistent changes
                if (weight_filter_->consecutiveChanges >= 3) {
                    weight_filter_->lastValidValue = raw_weight;
                    add_to_window(weight_filter_.get(), raw_weight);
                    weight_filter_->lastChange = relativeChange;
                    return raw_weight;
                }
            } else {
                weight_filter_->consecutiveChanges = 1;
            }
        } else {
            weight_filter_->consecutiveChanges = 1;
        }
        
        weight_filter_->lastChange = relativeChange;
        // log_handler_->debug("Spike detected! Value: {}, Mean: {}, Change: {:.2f}%", 
        //                   raw_weight, mean, absChange * 100);
        return weight_filter_->lastValidValue;
    }
    
    // Normal value processing
    weight_filter_->consecutiveChanges = 0;
    weight_filter_->lastChange = relativeChange;
    weight_filter_->lastValidValue = raw_weight;
    add_to_window(weight_filter_.get(), raw_weight);
    return raw_weight;
}
void Weighing::thread_publisher() {
    uorb::PublicationData<uorb::msg::sensor_weight> pub_sensor_weight;
    
    while (!is_running_) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    while (is_running_) {
        int32_t raw_value;
        if (ioctl(fd_, HX711_GET_READING, &raw_value) < 0) {
            // log_handler_->error("Failed to read value");
            std::this_thread::sleep_for(std::chrono::milliseconds(10000));
            continue;
        }
        
        // Calculate raw weight
        float raw_weight = (raw_value - calib_.offset) / calib_.scale_factor;
        
        // Apply filter
        float filtered_weight = filter_weight_signal(raw_weight);
        
        auto &data = pub_sensor_weight.get();
        data.timestamp = orb_absolute_time_us();
        data.device_id = 0;
        data.weight = filtered_weight;
        
        if (!pub_sensor_weight.Publish()) {
            log_handler_->error("Failed to publish weight data");
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
}

bool Weighing::calibrate() {
  if (!calibration_mode_) {
    spdlog::error("Not in calibration mode");
    return false;
  }

      spdlog::info("Starting calibration...");

      // Set Gain
      if (ioctl(fd_, HX711_SET_GAIN, HX711_GAIN_128) < 0) {
        spdlog::error("Failed to set gain");
        return false;
      }

      // Get offset
      if (ioctl(fd_, HX711_CALI_OFFSET, &calib_.offset) < 0) {
        spdlog::error("Failed to calibrate offset");
        return false;
      }

      spdlog::info("offset: {}", calib_.offset);

      spdlog::info("Please ensure the scale is empty");
      std::this_thread::sleep_for(std::chrono::seconds(5));

      spdlog::info("Place a 5000g weight on the scale");
      std::this_thread::sleep_for(std::chrono::seconds(5));

      // Get scale factor
      int32_t factor_temp = 0;
      if (ioctl(fd_, HX711_CALI_FACTOR, &factor_temp) < 0) {
        spdlog::error("Failed to calibrate scale factor");
        return false;
      }
      calib_.scale_factor =
          static_cast<float>(factor_temp) / calib_.calibration_weight;
      spdlog::info("Calibration completed, the weight is {}", calib_.scale_factor);

      return save_calibration();
    }

bool Weighing::load_calibration() {
  try {
    json_object *root = json_object_from_file(calib_file_.c_str());
    if (!root) {
      return false;
    }

        json_object *j_offset, *j_scale_factor;
        if (json_object_object_get_ex(root, "offset", &j_offset)) {
          calib_.offset = json_object_get_int(j_offset);
        }
        if (json_object_object_get_ex(root, "scale_factor", &j_scale_factor)) {
          calib_.scale_factor = json_object_get_double(j_scale_factor);
        }

        json_object_put(root);
        return true;
      } catch (const std::exception &e) {
        spdlog::error("Failed to load calibration: {}", e.what());
        return false;
      }
    }

bool Weighing::save_calibration() {
  try {
    std::filesystem::create_directories(
        std::filesystem::path(calib_file_).parent_path());

        json_object *root = json_object_new_object();
        json_object_object_add(root, "offset", json_object_new_int(calib_.offset));
        json_object_object_add(root, "scale_factor",
                               json_object_new_double(calib_.scale_factor));

        int ret = json_object_to_file_ext(calib_file_.c_str(), root,
                                          JSON_C_TO_STRING_PRETTY);
        json_object_put(root);

    return ret == 0;
  } catch (const std::exception &e) {
    spdlog::error("Failed to save calibration: {}", e.what());
    return false;
  }
}

} // namespace aby_box
