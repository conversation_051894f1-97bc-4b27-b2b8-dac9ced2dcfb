// Test file to verify the optimizations work correctly
// This file can be used for manual testing of the enhanced features

#include "utils/resource_manager.hpp"
#include "utils/thread_safety.hpp"
#include "utils/safe_file_ops.hpp"
#include "utils/system_monitor.hpp"
#include "common/base_module.hpp"
#include <iostream>
#include <thread>
#include <chrono>

using namespace aby_box;

// Test module for demonstration
class TestModule : public BaseModule {
public:
    TestModule() : BaseModule("TestModule") {}
    
    bool init() override {
        log_handler_->info("TestModule initialized");
        setState(ModuleState::INITIALIZED);
        return true;
    }
    
    bool start() override {
        log_handler_->info("TestModule started");
        setState(ModuleState::RUNNING);
        return true;
    }
    
    bool stop() override {
        log_handler_->info("TestModule stopped");
        setState(ModuleState::STOPPED);
        return true;
    }
    
    void join() override {
        // Nothing to join for this test module
    }
};

void testResourceManager() {
    std::cout << "Testing Resource Manager..." << std::endl;
    
    auto& rm = ResourceManager::getInstance();
    
    // Register a test cleanup function
    rm.registerCleanup("test_cleanup", []() {
        std::cout << "Test cleanup function executed" << std::endl;
    });
    
    // Get memory stats
    auto stats = rm.getMemoryStats();
    std::cout << "Memory usage: " << stats.memory_usage_percent << "%" << std::endl;
    std::cout << "Process memory: " << stats.process_memory_kb << " KB" << std::endl;
    
    // Test FD count
    int fd_count = rm.getCurrentFdCount();
    std::cout << "Current FD count: " << fd_count << std::endl;
}

void testThreadSafety() {
    std::cout << "Testing Thread Safety..." << std::endl;
    
    // Test thread-safe counter
    ThreadSafeCounter counter(0);
    
    std::vector<std::thread> threads;
    for (int i = 0; i < 5; ++i) {
        threads.emplace_back([&counter]() {
            for (int j = 0; j < 100; ++j) {
                counter.increment();
            }
        });
    }
    
    for (auto& t : threads) {
        t.join();
    }
    
    std::cout << "Counter final value: " << counter.get() << " (expected: 500)" << std::endl;
    
    // Test thread-safe flag
    ThreadSafeFlag flag(false);
    flag.set();
    std::cout << "Flag is set: " << (flag.is_set() ? "true" : "false") << std::endl;
}

void testSafeFileOps() {
    std::cout << "Testing Safe File Operations..." << std::endl;
    
    auto& safe_ops = getSafeFileOps();
    
    // Test atomic write
    std::string test_content = "This is a test file content for atomic write operation.";
    std::string test_path = "/tmp/test_atomic_write.txt";
    
    auto result = safe_ops.writeFileAtomic(test_path, test_content);
    if (result == FileOpResult::SUCCESS) {
        std::cout << "Atomic write successful" << std::endl;
        
        // Test read
        auto read_content = safe_ops.readFileContent(test_path);
        if (read_content && *read_content == test_content) {
            std::cout << "File read successful and content matches" << std::endl;
        } else {
            std::cout << "File read failed or content mismatch" << std::endl;
        }
        
        // Clean up
        std::filesystem::remove(test_path);
    } else {
        std::cout << "Atomic write failed: " << SafeFileOps::getErrorString(result) << std::endl;
    }
}

void testSystemMonitor() {
    std::cout << "Testing System Monitor..." << std::endl;
    
    auto& monitor = getSystemMonitor();
    
    // Start monitoring
    monitor.startMonitoring(std::chrono::seconds(1));
    
    // Wait a bit for metrics to be collected
    std::this_thread::sleep_for(std::chrono::seconds(2));
    
    // Get current metrics
    auto metrics = monitor.getCurrentMetrics();
    std::cout << "CPU usage: " << metrics.cpu_usage_percent << "%" << std::endl;
    std::cout << "Memory usage: " << metrics.memory_usage_percent << "%" << std::endl;
    std::cout << "Disk usage: " << metrics.disk_usage_percent << "%" << std::endl;
    std::cout << "Network connected: " << (metrics.network_connected ? "yes" : "no") << std::endl;
    
    // Test alert system
    monitor.addAlert(AlertLevel::INFO, "TEST", "This is a test alert", "Test details");
    
    auto alerts = monitor.getUnacknowledgedAlerts();
    std::cout << "Unacknowledged alerts: " << alerts.size() << std::endl;
    
    // Stop monitoring
    monitor.stopMonitoring();
}

void testBaseModule() {
    std::cout << "Testing Enhanced Base Module..." << std::endl;
    
    auto module1 = std::make_shared<TestModule>();
    auto module2 = std::make_shared<TestModule>();
    
    // Test module lifecycle
    module1->init();
    module1->start();
    
    std::cout << "Module1 state: " << static_cast<int>(module1->getState()) << std::endl;
    std::cout << "Module1 is running: " << (module1->isRunning() ? "yes" : "no") << std::endl;
    std::cout << "Module1 name: " << module1->getModuleName() << std::endl;
    
    // Test dependency management
    module2->addDependency(module1);
    module2->init();
    
    // Test graceful stop
    module1->gracefulStop();
    module2->gracefulStop();
    
    std::cout << "Module lifecycle test completed" << std::endl;
}

int main() {
    std::cout << "=== ABY Box Optimization Tests ===" << std::endl;
    
    try {
        testResourceManager();
        std::cout << std::endl;
        
        testThreadSafety();
        std::cout << std::endl;
        
        testSafeFileOps();
        std::cout << std::endl;
        
        testSystemMonitor();
        std::cout << std::endl;
        
        testBaseModule();
        std::cout << std::endl;
        
        std::cout << "All tests completed successfully!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
