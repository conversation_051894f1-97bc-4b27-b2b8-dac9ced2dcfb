#!/bin/bash

# OTA 包创建和推送脚本 - 为 ext4 镜像生成 block map 文件并推送到 MinIO
# 用法: ./push_ota_package.sh
# 功能: 自动从配置文件获取version_id，生成OTA包文件，并使用mc工具推送到MinIO

# 配置
LOG_FILE="/home/<USER>/push_ota_package.log"
IMAGE_BASE_PATH="/home/<USER>/animsi/aby/yocto/build/tmp/deploy/images/aby-box-arm"
CONFIG_FILE="/home/<USER>/animsi/aby/aby_box/cfg/aby_box/config.json"

# MinIO/mc 相关配置
MINIO_ALIAS="minio"  # mc配置的MinIO别名
MC_CONFIG_DIR="/home/<USER>/animsi/aby/aby_box/cfg/ota_mc"  # mc配置目录
OTA_BUCKET="otaworkspace"    # OTA文件存储的bucket名称
MAX_RETRIES=3
RETRY_DELAY=5

# 日志函数
log() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "$timestamp - $1" | tee -a "$LOG_FILE"
}

# 检查必要工具
check_tools() {
    log "检查必要工具..."
    
    # 检查jq是否安装
    if ! command -v jq &> /dev/null; then
        log "错误: 未找到 jq 工具，请安装 jq 包"
        echo "可以使用 'sudo apt-get install jq' 安装"
        exit 1
    fi
    
    # 检查bmaptool是否安装
    if ! command -v bmaptool &> /dev/null; then
        log "错误: 未找到 bmaptool 工具，请安装 bmap-tools 包"
        exit 1
    fi
    
    # 检查 xz
    if ! command -v xz &> /dev/null; then
        log "错误: 未找到 xz 工具"
        exit 1
    fi
    
    # 检查 mc (MinIO Client)
    if ! command -v mc &> /dev/null; then
        log "错误: 未找到 mc 工具"
        
        # 尝试在用户主目录安装mc
        local MC_INSTALL_DIR="$HOME/bin"
        local MC_PATH="$MC_INSTALL_DIR/mc"
        
        log "尝试在用户目录安装 MinIO Client: $MC_PATH"
        
        # 创建用户bin目录
        mkdir -p "$MC_INSTALL_DIR"
        
        # 下载mc工具到用户目录
        if curl -f https://dl.min.io/client/mc/release/linux-amd64/mc \
           -o "$MC_PATH" 2>/dev/null; then
            chmod +x "$MC_PATH"
            
            # 将用户bin目录添加到PATH中（如果还没有的话）
            if [[ ":$PATH:" != *":$MC_INSTALL_DIR:"* ]]; then
                export PATH="$MC_INSTALL_DIR:$PATH"
                log "已将 $MC_INSTALL_DIR 添加到PATH中"
            fi
            
            # 验证安装是否成功
            if command -v mc &> /dev/null; then
                log "mc 工具安装成功: $(which mc)"
            else
                log "错误: mc 工具安装后仍无法找到"
                log "请手动安装 MinIO Client 或将 $MC_PATH 添加到PATH中"
                exit 1
            fi
        else
            log "错误: 无法下载 mc 工具"
            log "请手动安装 MinIO Client:"
            log "  wget https://dl.min.io/client/mc/release/linux-amd64/mc -O ~/bin/mc"
            log "  chmod +x ~/bin/mc"
            log "  export PATH=\$HOME/bin:\$PATH"
            exit 1
        fi
    fi
    
    # 检查 zip
    if ! command -v zip &> /dev/null; then
        log "错误: 未找到 zip 工具，请安装 zip 包"
        exit 1
    fi
    
    log "所有必要工具检查通过"
}

# 初始化mc配置
init_mc_config() {
    log "初始化 MinIO Client 配置..."
    
    # 检查 mc 配置目录
    if [ -n "$MC_CONFIG_DIR" ]; then
        export MC_CONFIG_DIR
        log "设置 MC_CONFIG_DIR=$MC_CONFIG_DIR"
    fi
    
    # 检查 mc 配置是否有效
    if ! mc --config-dir="$MC_CONFIG_DIR" alias ls "$MINIO_ALIAS" &>/dev/null; then
        log "错误: MinIO别名 $MINIO_ALIAS 未配置或无效"
        exit 1
    fi
    
    log "MinIO Client 配置验证成功"
}

# 使用mc上传文件
upload_with_mc() {
    local local_path="$1"
    local remote_path="$2"
    local description="$3"
    
    log "正在上传 $description: $local_path -> $remote_path"
    
    local retry_count=0
    local upload_successful=false
    
    while [ $retry_count -lt $MAX_RETRIES ]; do
        # 使用mc cp命令上传文件
        nice -n 19 ionice -c 3 mc --config-dir="$MC_CONFIG_DIR" cp \
            --quiet \
            "$local_path" \
            "${MINIO_ALIAS}/${OTA_BUCKET}/${remote_path}"
        
        local result=$?
        if [ $result -eq 0 ]; then
            log "$description 上传成功: $remote_path"
            upload_successful=true
            break
        fi
        
        retry_count=$((retry_count + 1))
        if [ $retry_count -lt $MAX_RETRIES ]; then
            log "上传失败，将在 $RETRY_DELAY 秒后重试... (尝试 $retry_count/$MAX_RETRIES)"
            sleep $RETRY_DELAY
        fi
    done
    
    if [ "$upload_successful" = false ]; then
        log "错误: $description 上传失败，$MAX_RETRIES 次尝试后放弃"
        return 1
    fi
    
    return 0
}

# 检查并创建bucket
check_create_bucket() {
    log "检查 bucket ${OTA_BUCKET} 是否存在..."
    
    # 使用mc ls命令检查bucket是否存在
    if nice -n 19 ionice -c 3 mc --config-dir="$MC_CONFIG_DIR" ls "${MINIO_ALIAS}/${OTA_BUCKET}" &>/dev/null; then
        log "Bucket ${OTA_BUCKET} 存在"
        return 0
    else
        log "Bucket ${OTA_BUCKET} 不存在，正在创建..."
        # 创建新的 bucket
        nice -n 19 ionice -c 3 mc --config-dir="$MC_CONFIG_DIR" mb --quiet "${MINIO_ALIAS}/${OTA_BUCKET}"
        
        if [ $? -eq 0 ]; then
            log "Bucket ${OTA_BUCKET} 创建成功"
            return 0
        else
            log "错误: 创建 bucket ${OTA_BUCKET} 失败"
            return 1
        fi
    fi
}

# 生成OTA包文件
generate_ota_package() {
    log "开始生成 OTA 包文件..."
    
    # 检查配置文件是否存在
    if [ ! -f "$CONFIG_FILE" ]; then
        log "错误: 配置文件不存在: $CONFIG_FILE"
        exit 1
    fi
    
    # 从配置文件读取版本ID
    VERSION_ID=$(jq -r '.version_id' "$CONFIG_FILE")
    if [ -z "$VERSION_ID" ] || [ "$VERSION_ID" == "null" ]; then
        log "错误: 无法从配置文件获取 version_id"
        exit 1
    fi
    
    ROOTFS_PREFIX="core-image-minimal-aby-box-arm.rootfs-"
    TARGET_ROOTFS_NAME="${ROOTFS_PREFIX}${VERSION_ID}"
    TARGET_PATH="${IMAGE_BASE_PATH}/${TARGET_ROOTFS_NAME}.ext4"
    
    # 为所有生成的文件创建目录
    OUTPUT_DIR="${IMAGE_BASE_PATH}/${TARGET_ROOTFS_NAME}"
    mkdir -p "$OUTPUT_DIR"
    
    log "从配置文件读取版本 ID: $VERSION_ID"
    log "目标文件名: ${TARGET_ROOTFS_NAME}.ext4"
    log "输出目录: $OUTPUT_DIR"
    
    # 查找最新的 ext4 镜像文件
    log "查找最新的 ext4 镜像文件..."
    FOUND_EXT4=$(find "$IMAGE_BASE_PATH" -name "${ROOTFS_PREFIX}*.ext4" -type f -print -quit)
    
    if [ -z "$FOUND_EXT4" ]; then
        log "错误: 在 $IMAGE_BASE_PATH 中未找到任何 ext4 镜像文件"
        exit 1
    fi
    
    log "找到镜像文件: $FOUND_EXT4"
    
    # 如果找到的文件与目标文件不同，则创建副本或创建符号链接
    if [ "$FOUND_EXT4" != "$TARGET_PATH" ]; then
        log "找到的文件名与目标文件名不同，将创建符号链接"
        
        # 如果目标文件已存在，则删除
        if [ -e "$TARGET_PATH" ]; then
            log "目标文件 $TARGET_PATH 已存在，将被删除"
            rm -f "$TARGET_PATH"
            if [ $? -ne 0 ]; then
                log "错误: 无法删除已存在的目标文件"
                exit 1
            fi
        fi
        
        # 创建符号链接
        ln -sf "$FOUND_EXT4" "$TARGET_PATH"
        if [ $? -ne 0 ]; then
            log "错误: 无法创建符号链接，尝试复制文件..."
            cp "$FOUND_EXT4" "$TARGET_PATH"
            if [ $? -ne 0 ]; then
                log "错误: 无法复制文件到 $TARGET_PATH"
                exit 1
            fi
            log "文件复制成功"
        else
            log "符号链接创建成功"
        fi
    fi
    
    # 设置工作文件路径
    EXT4_IMAGE="$TARGET_PATH"
    BMAP_FILE="${OUTPUT_DIR}/${TARGET_ROOTFS_NAME}.ext4.bmap"
    
    # 检查文件是否存在
    if [ ! -f "$EXT4_IMAGE" ]; then
        log "错误: ext4镜像文件不存在: $EXT4_IMAGE"
        exit 1
    fi
    
    # 生成bmap文件
    log "为 $EXT4_IMAGE 生成 bmap 文件..."
    
    # 运行bmaptool创建bmap文件
    if bmaptool create -o "$BMAP_FILE" "$EXT4_IMAGE"; then
        log "bmap文件创建成功: $BMAP_FILE"
        
        # 获取文件大小作为简单检查
        bmap_size=$(du -h "$BMAP_FILE" | awk '{print $1}')
        ext4_size=$(du -h "$EXT4_IMAGE" | awk '{print $1}')
        log "bmap文件大小: $bmap_size, ext4文件大小: $ext4_size"
        
        # 创建MD5校验文件 - 为bmap文件生成校验和
        md5sum "$BMAP_FILE" | awk '{print $1}' > "${BMAP_FILE}.md5"
        log "创建了bmap的MD5校验文件: ${BMAP_FILE}.md5"
        
        # 为未压缩的ext4文件创建MD5校验和
        log "为未压缩的ext4文件创建MD5校验和..."
        EXT4_MD5_FILE="${OUTPUT_DIR}/${TARGET_ROOTFS_NAME}.ext4.md5"
        md5sum "$EXT4_IMAGE" | awk '{print $1}' > "$EXT4_MD5_FILE"
        log "创建了未压缩ext4文件的MD5校验文件: $EXT4_MD5_FILE"
        
        # 检查是否存在压缩文件，如果不存在则创建
        XZ_FILE="${OUTPUT_DIR}/${TARGET_ROOTFS_NAME}.ext4.xz"
        if [ -f "${EXT4_IMAGE}.xz" ]; then
            log "找到已存在的压缩文件: ${EXT4_IMAGE}.xz，复制到输出目录"
            cp "${EXT4_IMAGE}.xz" "$XZ_FILE"
        else
            log "未找到压缩文件，创建新的压缩文件..."
            if command -v xz &> /dev/null; then
                log "开始压缩镜像文件到 $XZ_FILE..."
                xz -k -v -c "$EXT4_IMAGE" > "$XZ_FILE"
                if [ $? -ne 0 ]; then
                    log "压缩失败"
                    exit 1
                fi
                log "压缩成功: $XZ_FILE"
            else
                log "错误: 未找到 xz 工具，无法压缩"
                exit 1
            fi
        fi
        
        # 为压缩文件创建MD5校验和
        md5sum "$XZ_FILE" | awk '{print $1}' > "${XZ_FILE}.md5"
        log "创建了压缩镜像文件的MD5校验文件: ${XZ_FILE}.md5"
        
        # 创建一个版本文件
        echo "$VERSION_ID" > "${OUTPUT_DIR}/version"
        log "创建了版本文件: ${OUTPUT_DIR}/version"
        
        # 显示输出目录内容
        log "输出目录内容:"
        ls -la "$OUTPUT_DIR" | tee -a "$LOG_FILE"
        
        log "OTA包文件生成完成"
        return 0
    else
        log "生成 bmap 文件失败"
        exit 1
    fi
}

# 上传OTA包到MinIO
upload_ota_package() {
    log "开始创建并上传 OTA zip 包到 MinIO..."
    
    # 确保bucket存在
    if ! check_create_bucket; then
        log "错误: 无法确保bucket存在"
        exit 1
    fi
    
    # 创建zip包文件名
    local ZIP_FILE_NAME="${TARGET_ROOTFS_NAME}.zip"
    local ZIP_FILE_PATH="${IMAGE_BASE_PATH}/${ZIP_FILE_NAME}"
    
    log "创建OTA zip包: $ZIP_FILE_PATH"
    
    # 进入输出目录，这样zip包内的文件路径会是相对路径
    cd "$OUTPUT_DIR" || {
        log "错误: 无法进入输出目录 $OUTPUT_DIR"
        return 1
    }
    
    # 创建zip包，包含所有OTA相关文件
    # 使用相对路径，这样解压后的目录结构与原来保持一致
    local files_to_zip=(
        "${TARGET_ROOTFS_NAME}.ext4.xz"
        "${TARGET_ROOTFS_NAME}.ext4.xz.md5"
        "${TARGET_ROOTFS_NAME}.ext4.md5"
        "${TARGET_ROOTFS_NAME}.ext4.bmap"
        "${TARGET_ROOTFS_NAME}.ext4.bmap.md5"
        "version"
    )
    
    # 检查所有必需文件是否存在
    local missing_files=false
    for file in "${files_to_zip[@]}"; do
        if [ ! -f "$file" ]; then
            log "错误: 缺少必需文件: $file"
            missing_files=true
        fi
    done
    
    if [ "$missing_files" = true ]; then
        log "错误: 存在缺失文件，无法创建zip包"
        return 1
    fi
    
    # 创建zip包
    log "正在创建zip包，包含以下文件:"
    for file in "${files_to_zip[@]}"; do
        log "  - $file"
    done
    
    if zip -r "$ZIP_FILE_PATH" "${files_to_zip[@]}"; then
        log "zip包创建成功: $ZIP_FILE_PATH"
        
        # 显示zip包信息
        local zip_size=$(du -h "$ZIP_FILE_PATH" | awk '{print $1}')
        log "zip包大小: $zip_size"
        
        # 验证zip包内容
        log "zip包内容:"
        zipinfo "$ZIP_FILE_PATH" | tee -a "$LOG_FILE"
    else
        log "错误: zip包创建失败"
        return 1
    fi
    
    # 回到原来的目录
    cd - > /dev/null
    
    # 为zip包创建MD5校验和
    local ZIP_MD5_FILE="${ZIP_FILE_PATH}.md5"
    md5sum "$ZIP_FILE_PATH" | awk '{print $1}' > "$ZIP_MD5_FILE"
    log "创建了zip包的MD5校验文件: $ZIP_MD5_FILE"
    
    # 上传zip包
    log "上传zip包到MinIO..."
    if ! upload_with_mc "$ZIP_FILE_PATH" "$ZIP_FILE_NAME" "OTA zip包"; then
        log "错误: zip包上传失败"
        return 1
    fi
    
    # 上传zip包的MD5校验和
    if ! upload_with_mc "$ZIP_MD5_FILE" "${ZIP_FILE_NAME}.md5" "zip包MD5校验和"; then
        log "错误: zip包MD5校验和上传失败"
        return 1
    fi
    
    log "OTA zip包上传成功"
    return 0
}

# 更新最新版本标记
update_latest_version() {
    log "更新最新版本标记..."
    
    # 创建临时的version文件
    local temp_version_file="/tmp/last_release_version_$$"
    echo "$VERSION_ID" > "$temp_version_file"
    
    # 上传最新版本标记文件
    if upload_with_mc "$temp_version_file" "last_release_version" "最新版本标记文件"; then
        log "最新版本标记已更新为: $VERSION_ID"
        rm -f "$temp_version_file"
        return 0
    else
        log "错误: 更新最新版本标记失败"
        rm -f "$temp_version_file"
        return 1
    fi
}

# 清理临时文件
cleanup() {
    log "清理临时文件..."
    
    # 可选择是否保留生成的文件
    read -p "是否删除本地生成的OTA包文件和zip包? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log "删除本地OTA包文件..."
        rm -rf "$OUTPUT_DIR"
        
        # 删除生成的zip包及其MD5文件
        local ZIP_FILE_PATH="${IMAGE_BASE_PATH}/${TARGET_ROOTFS_NAME}.zip"
        local ZIP_MD5_FILE="${ZIP_FILE_PATH}.md5"
        
        if [ -f "$ZIP_FILE_PATH" ]; then
            rm -f "$ZIP_FILE_PATH"
            log "删除了zip包文件: $ZIP_FILE_PATH"
        fi
        
        if [ -f "$ZIP_MD5_FILE" ]; then
            rm -f "$ZIP_MD5_FILE"
            log "删除了zip包MD5文件: $ZIP_MD5_FILE"
        fi
        
        log "本地文件已清理"
    else
        log "保留本地OTA包文件: $OUTPUT_DIR"
        log "保留生成的zip包: ${IMAGE_BASE_PATH}/${TARGET_ROOTFS_NAME}.zip"
    fi
}

# 验证上传结果
verify_upload() {
    log "验证上传结果..."
    
    # 检查远程文件列表
    log "检查远程bucket中的zip包文件..."
    nice -n 19 ionice -c 3 mc --config-dir="$MC_CONFIG_DIR" ls "${MINIO_ALIAS}/${OTA_BUCKET}/${TARGET_ROOTFS_NAME}.zip" | tee -a "$LOG_FILE"
    nice -n 19 ionice -c 3 mc --config-dir="$MC_CONFIG_DIR" ls "${MINIO_ALIAS}/${OTA_BUCKET}/${TARGET_ROOTFS_NAME}.zip.md5" | tee -a "$LOG_FILE"
    
    # 检查last_release_version文件
    log "检查最新版本标记文件..."
    nice -n 19 ionice -c 3 mc --config-dir="$MC_CONFIG_DIR" cat "${MINIO_ALIAS}/${OTA_BUCKET}/last_release_version" | tee -a "$LOG_FILE"
    
    log "验证完成"
}

# 主函数
main() {
    log "======= OTA 包创建和推送开始 ======="
    
    # 检查工具
    check_tools
    
    # 初始化MinIO Client配置
    init_mc_config
    
    # 生成OTA包文件
    generate_ota_package
    
    # 上传OTA包到MinIO
    if ! upload_ota_package; then
        log "错误: OTA包上传失败"
        exit 1
    fi
    
    # 更新最新版本标记
    if ! update_latest_version; then
        log "错误: 更新最新版本标记失败"
        exit 1
    fi
    
    # 验证上传结果
    verify_upload
    
    log "======= OTA 包创建和推送完成 ======="
    log "版本 $VERSION_ID 的OTA zip包已成功推送到MinIO"
    log "远程bucket: ${MINIO_ALIAS}/${OTA_BUCKET}"
    log "OTA zip包文件: ${TARGET_ROOTFS_NAME}.zip"
    log "OTA zip包MD5文件: ${TARGET_ROOTFS_NAME}.zip.md5"
    log "最新版本标记已更新"
    
    # 清理临时文件
    cleanup
    
    log "推送操作完成"
}

# 执行主函数
main "$@" 