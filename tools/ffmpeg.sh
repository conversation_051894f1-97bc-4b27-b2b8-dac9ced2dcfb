#!/bin/bash

# 配置变量
RTSP_URL="rtsp://127.0.0.1/h264"
BASE_PATH="/mnt/recordings"
PID_FILE="/tmp/rtsp_recorder.pid"
LOG_FILE="/var/log/rtsp_recorder.log"
    
# 确保基础录制目录存在
mkdir -p "$BASE_PATH"

# 日志函数
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
    echo "$1"
}

# 获取录制路径
get_record_path() {
    local timestamp=""
    
    # 检查是否提供了时间戳参数
    if [ -n "$2" ]; then
        # 将秒级时间戳转换为 %Y-%m-%d_%H-%M-%S 格式
        timestamp=$(date -d "@$2" '+%Y-%m-%d_%H-%M-%S' 2>/dev/null)
        
        # 检查转换是否成功
        if [ $? -ne 0 ]; then
            log "警告: 无法解析时间戳 '$2', 使用当前时间"
            timestamp=$(date '+%Y-%m-%d_%H-%M-%S')
        fi
    else
        # 如果没有提供时间戳，使用当前时间
        timestamp=$(date '+%Y-%m-%d_%H-%M-%S')
    fi
    
    local record_path="$BASE_PATH/${timestamp}_hls"
    
    mkdir -p "$record_path"
    echo "$record_path"
}

# 开始录制
start_recording() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            log "Already recording (PID: $pid)"
            return 1
        fi
    fi

    local record_path=$(get_record_path "$@")
    log "Starting recording to $record_path"

    # 启动 FFmpeg - 使用 nice 和 ionice 提高进程优先级
    nice -n -15 ionice -c 1 -n 0 ffmpeg -rtsp_transport tcp \
        -i "$RTSP_URL" \
        -c:v copy \
        -c:a libfdk_aac -b:a 32k -ar 8000 -afterburner 0 \
        -f hls \
        -hls_time 6 \
        -hls_playlist_type vod \
        -hls_list_size 0 \
        -hls_segment_filename "$record_path/segment_%04d.ts" \
        -hls_flags delete_segments \
        "$record_path/playlist.m3u8" \
        </dev/null >"$record_path/ffmpeg.log" 2>&1 &

    # 保存进程 ID
    echo $! > "$PID_FILE"
    log "Recording started with PID: $! in directory: $record_path"
}

# 停止录制
stop_recording() {
    if [ ! -f "$PID_FILE" ]; then
        log "No recording in progress"
        return 1
    fi

    local pid=$(cat "$PID_FILE")
    if ! kill -0 "$pid" 2>/dev/null; then
        log "No recording process found"
        rm -f "$PID_FILE"
        return 1
    fi

    # 先尝试优雅地停止
    log "Stopping recording (PID: $pid)"
    kill -TERM "$pid"

    # 等待进程结束
    local count=0
    while kill -0 "$pid" 2>/dev/null && [ $count -lt 5 ]; do
        sleep 1
        count=$((count + 1))
    done

    # 如果进程还在运行，强制终止
    if kill -0 "$pid" 2>/dev/null; then
        log "Force stopping recording"
        kill -9 "$pid"
    fi

    rm -f "$PID_FILE"
    log "Recording stopped"
}

# 检查录制状态
check_status() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            echo "Recording is active (PID: $pid)"
            return 0
        else
            echo "Recording process not found (stale PID file)"
            rm -f "$PID_FILE"
            return 1
        fi
    else
        echo "No recording in progress"
        return 1
    fi
}



# 主函数
case "$1" in
    start)
        start_recording "$@"  # 将所有参数传递给 start_recording
        ;;
    stop)
        stop_recording
        ;;
    status)
        check_status
        ;;

    *)
        echo "Usage: $0 {start [timestamp_in_seconds]|stop|status}"
        echo "Example: $0 start 1625097600  # 时间戳对应 2021-07-01_00-00-00"
        exit 1
        ;;
esac

exit 0
