#!/bin/bash

LAST_DATE_FILE="/etc/last_date"
LOG_FILE="/var/log/record_time.log"
CHECK_INTERVAL=60

log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

# 确保日志文件存在
touch "$LOG_FILE"
log "启动时间记录服务"

while true; do
    # 获取当前系统时间戳
    CURRENT_TIME=$(date +%s)
    
    # 检查是否存在上次记录的时间
    if [ -f "$LAST_DATE_FILE" ]; then
        # 读取上次记录的时间戳
        if LAST_TIME=$(cat "$LAST_DATE_FILE" 2>/dev/null); then
            # 比较两个时间，取较大值
            if [ "$LAST_TIME" -gt "$CURRENT_TIME" ]; then
                log "发现存储的时间($LAST_TIME)大于系统时间($CURRENT_TIME)，更新系统时间"
                # 使用存储的时间更新系统时间
                date -s "@$LAST_TIME" > /dev/null 2>&1
                CURRENT_TIME=$LAST_TIME
            else
                log "系统时间($CURRENT_TIME)正常，大于等于存储的时间($LAST_TIME)"
            fi
        else
            log "无法读取上次记录的时间，使用当前系统时间"
        fi
    else
        log "未找到时间记录文件，创建新文件"
    fi
    
    # 将当前时间写入记录文件
    echo "$CURRENT_TIME" > "$LAST_DATE_FILE"
    log "时间记录已更新: $(date -d @$CURRENT_TIME)"
    
    # 等待指定的时间间隔
    sleep $CHECK_INTERVAL
done