#!/bin/bash

# 配置
LOG_FILE="/var/log/cpuusage.log"
PIDFILE="/var/run/cpu_monitor.pid"
CHECK_INTERVAL=0.3  # CPU占用记录间隔 (秒)

# 记录CPU使用率
record_cpu_usage() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local cpu_usage
    
    if command -v mpstat > /dev/null 2>&1; then
        # 使用mpstat获取CPU使用率 (100% - idle%)
        cpu_usage=$(mpstat 1 1 | grep "Average" | grep "all" | awk '{print 100 - $12}' | cut -d. -f1)
    else
        # 备用方案：使用top命令
        cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2 + $4}' | cut -d. -f1)
    fi
    
    # 简单地记录时间戳和CPU使用率
    echo "$timestamp $cpu_usage%" >> "$LOG_FILE"
}

# 主服务循环
run_service() {
    while true; do
        record_cpu_usage
        sleep $CHECK_INTERVAL
    done
}

# 启动服务的主函数
start_service() {
    # 检查服务是否已经在运行
    if [ -f "$PIDFILE" ]; then
        pid=$(cat "$PIDFILE")
        if kill -0 "$pid" 2>/dev/null; then
            echo "CPU监控服务已经在运行，PID: $pid"
            return 1
        else
            rm -f "$PIDFILE"
        fi
    fi
    
    # 创建日志文件（如果不存在）
    touch "$LOG_FILE"
    
    # 以守护进程方式启动
    nohup "$0" daemon > /dev/null 2>&1 &
    echo $! > "$PIDFILE"
    echo "CPU监控服务已启动, PID: $(cat "$PIDFILE")"
    return 0
}

# 守护进程模式的入口点
daemon_mode() {
    run_service
    rm -f "$PIDFILE"
    exit 1
}

# 处理服务控制命令
case "$1" in
    start)
        start_service
        ;;
    daemon)
        daemon_mode
        ;;
    stop)
        if [ -f "$PIDFILE" ]; then
            echo "停止CPU监控服务..."
            kill $(cat "$PIDFILE")
            rm -f "$PIDFILE"
            echo "CPU监控服务已停止"
        else
            echo "CPU监控服务未运行"
        fi
        ;;
    status)
        if [ -f "$PIDFILE" ] && kill -0 $(cat "$PIDFILE") 2>/dev/null; then
            echo "CPU监控服务正在运行, PID: $(cat "$PIDFILE")"
            exit 0
        else
            [ -f "$PIDFILE" ] && rm -f "$PIDFILE"
            echo "CPU监控服务未运行"
            exit 1
        fi
        ;;
    *)
        echo "用法: $0 {start|stop|status}"
        exit 1
        ;;
esac

exit 0
