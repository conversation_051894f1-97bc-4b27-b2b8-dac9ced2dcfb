#!/bin/bash

# OTA 升级脚本
# 功能：
# 1. 检测当前根分区，实现 A/B 分区切换
# 2. 使用本地OTA升级包(zip格式)并解压（由OtaManager下载）
# 3. 使用 bmaptool 写入 rootfs 到备用分区
# 4. 更新 uEnv.txt 中的引导配置以使用新分区
# 5. 保证配置文件的持久性，通过将配置保存在独立分区

# 配置变量
CONFIG_FILE="/etc/cfg/aby_box/config.json"
DEVICE="/dev/mmcblk0"
PARTITION_A="${DEVICE}p2"
PARTITION_B="${DEVICE}p3"
BOOT_PARTITION="${DEVICE}p1"
BOOT_MOUNT="/tmp/boot_mount"
UENV_FILE="${BOOT_MOUNT}/uEnv.txt"
LOG_FILE="/var/log/ota_upgrade.log"

# 从环境变量获取OTA配置
OTA_PACKAGE_PATH="${OTA_PACKAGE_PATH:-}"

TMP_DIR="/tmp/ota_update"
BMAP_FILE="/tmp/rootfs.bmap"  # bmaptool 映射文件
VERIFY_CHECKSUM=true # 是否验证MD5校验和
OTA_SUCCESS=false    # 跟踪OTA流程是否成功

# 持久化数据分区配置
PERSIST_PARTITION="${DEVICE}p4"  # 持久化数据分区
PERSIST_PARTITION_NUMBER="4"     # 对应mmcblk0p4
PERSIST_MOUNT="/tmp/persist_mount"  # 临时挂载点
CFG_DIR="/etc/cfg/aby_box"            # 需要保存的配置目录

# 日志函数
log() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "$timestamp - $1" | tee -a "$LOG_FILE"
}

# 检查是否为 root 用户
check_root() {
    if [ "$(id -u)" -ne 0 ]; then
        log "错误: 此脚本需要 root 权限，请使用 sudo 运行"
        exit 1
    fi
}

# 创建临时目录
setup_environment() {
    mkdir -p "$TMP_DIR"
    touch "$LOG_FILE"
    log "创建临时目录: $TMP_DIR"
}

# 检查必要的工具是否安装
check_tools() {
    log "检查必要工具..."
    
    # 检查 bmaptool
    if ! command -v bmaptool &> /dev/null; then
        log "错误: 未找到 bmaptool 工具。请安装 bmap-tools 包。"
        exit 1
    fi
    
    # 检查 unzip (解压ZIP文件必需)
    if ! command -v unzip &> /dev/null; then
        log "错误: 未找到 unzip 工具。"
        exit 1
    fi
    
    # 检查 xz
    if ! command -v xz &> /dev/null; then
        log "错误: 未找到 xz 工具。"
        exit 1
    fi
    
    # 检查 jq
    if ! command -v jq &> /dev/null; then
        log "错误: 未找到 jq 工具。"
        exit 1
    fi
    
    # 检查 partprobe
    if ! command -v partprobe &> /dev/null; then
        log "错误: 未找到 partprobe 工具。"
        exit 1
    fi
    
    # 检查 md5sum
    if ! command -v md5sum &> /dev/null; then
        log "错误: 未找到 md5sum 工具。"
        exit 1
    fi
    
    log "所有必要工具检查通过"
}

# 获取当前版本和目标版本
get_versions() {
    log "获取版本信息..."
    
    # 检查 config.json 是否存在
    if [ ! -f "$CONFIG_FILE" ]; then
        log "错误: 配置文件不存在: $CONFIG_FILE"
        exit 1
    fi
    
    # 从 config.json 获取当前版本
    CURRENT_VERSION=$(jq -r '.version_id' "$CONFIG_FILE")
    if [ -z "$CURRENT_VERSION" ] || [ "$CURRENT_VERSION" == "null" ]; then
        log "错误: 无法从配置文件获取当前版本"
        exit 1
    fi
    
    # 检查是否提供了本地OTA包路径
    if [ -z "$OTA_PACKAGE_PATH" ]; then
        log "错误: 需要通过环境变量 OTA_PACKAGE_PATH 提供本地OTA包路径"
        exit 1
    fi
    
    # 检查本地文件是否存在
    if [ ! -f "$OTA_PACKAGE_PATH" ]; then
        log "错误: 本地OTA包文件不存在: $OTA_PACKAGE_PATH"
        exit 1
    fi
    
    # 从OTA包文件名中提取版本信息
    local ota_filename=$(basename "$OTA_PACKAGE_PATH")
    
    # 尝试从文件名中提取版本（假设格式类似 core-image-minimal-aby-box-arm.rootfs-X.Y.Z.zip）
    if [[ "$ota_filename" =~ core-image-minimal-aby-box-arm\.rootfs-([0-9]+\.[0-9]+\.[0-9]+)\.zip$ ]]; then
        LATEST_VERSION="${BASH_REMATCH[1]}"
        log "从OTA包文件名提取的目标版本: $LATEST_VERSION"
    else
        log "警告: 无法从OTA包文件名提取版本信息，使用默认版本号"
        # 如果无法从文件名提取，使用时间戳作为版本号
        LATEST_VERSION="upgrade-$(date +%Y%m%d-%H%M%S)"
        log "使用生成的版本号: $LATEST_VERSION"
    fi
    
    log "当前版本: $CURRENT_VERSION"
    log "目标版本: $LATEST_VERSION"
    
    # 设置文件路径
    ROOTFS_FILE_NAME="core-image-minimal-aby-box-arm.rootfs-${LATEST_VERSION}"
    ROOTFS_XZ_FILE="$TMP_DIR/${ROOTFS_FILE_NAME}/${ROOTFS_FILE_NAME}.ext4.xz"
    ROOTFS_FILE="$TMP_DIR/${ROOTFS_FILE_NAME}/${ROOTFS_FILE_NAME}.ext4"
    
    log "检测到本地OTA包，将进行升级"
    return 0
}

# 验证文件MD5校验和
verify_md5() {
    local file="$1"
    local md5_file="$2"
    local file_name=$(basename "$file")
    
    log "验证文件完整性: $file_name"
    
    # 检查MD5文件是否存在
    if [ ! -f "$md5_file" ]; then
        log "错误: MD5校验文件不存在: $md5_file"
        return 1
    fi
    
    # 检查MD5文件格式，通常包含[MD5校验和] [文件名]
    local expected_md5=$(cat "$md5_file" | awk '{print $1}')
    if [ -z "$expected_md5" ]; then
        log "错误: 无法从校验文件解析 MD5 值"
        return 1
    fi
    
    # 计算文件MD5值
    log "计算文件 MD5 值..."
    local actual_md5=$(md5sum "$file" | awk '{print $1}')
    
    # 比较MD5值
    if [ "$expected_md5" = "$actual_md5" ]; then
        log "MD5校验通过: $file_name"
        return 0
    else
        log "错误: MD5校验失败"
        log "预期MD5: $expected_md5"
        log "实际MD5: $actual_md5"
        return 1
    fi
}

# 创建并初始化持久性数据分区
setup_persist_partition() {
    log "检查持久性数据分区 $PERSIST_PARTITION..."
    
    # 检查分区是否已存在
    if [ ! -b "$PERSIST_PARTITION" ]; then
        log "持久性数据分区不存在，正在创建..."
        
        # 获取磁盘信息
        local disk_info=$(fdisk -l "$DEVICE" 2>/dev/null)
        if [ -z "$disk_info" ]; then
            log "错误: 无法读取设备 $DEVICE 的信息"
            return 1
        fi
        
        log "检查磁盘空间和分区情况..."
        echo "$disk_info" | grep -E "Disk|${DEVICE}p" | tee -a "$LOG_FILE"
        
        # 获取磁盘总扇区数
        local total_sectors=$(fdisk -l "$DEVICE" | grep "sectors$" | awk '{print $7}')
        if [ -z "$total_sectors" ]; then
            log "错误: 无法获取磁盘总扇区数"
            return 1
        fi
        
        # 计算用于持久化分区的起始扇区（总扇区数 - 100MB对应的扇区数）
        # 100MB = 100*1024*1024 bytes = 204800 sectors (每个扇区512字节)
        local persist_start=$((total_sectors - 204800))
        log "磁盘总扇区数: $total_sectors, 将在最后100MB创建持久化分区，起始扇区: $persist_start"
        
        # 检查分区号是否已存在，如果存在则删除
        if fdisk -l "$DEVICE" | grep -q "${DEVICE}p${PERSIST_PARTITION_NUMBER}"; then
            log "分区 ${PERSIST_PARTITION_NUMBER} 已存在，将删除并重新创建"
            echo -e "d\n${PERSIST_PARTITION_NUMBER}\nw" | fdisk "$DEVICE"
            sleep 2
            partprobe "$DEVICE"
            sleep 1
        fi
        
        # 使用非交互式方式创建分区在磁盘末尾
        log "在磁盘末尾创建持久性数据分区，大小: 100MB..."
        (
            echo n    # 新建分区
            echo p    # 主分区
            echo $PERSIST_PARTITION_NUMBER    # 分区号
            echo $persist_start    # 起始扇区 (磁盘末尾前100MB)
            echo      # 默认结束扇区 (磁盘末尾)
            echo w    # 写入分区表
        ) | fdisk "$DEVICE"
        
        local fdisk_result=$?
        log "fdisk 命令返回值: $fdisk_result"
        
        # 等待分区表更新
        sleep 2
        
        # 强制内核重新读取分区表
        log "刷新分区表..."
        partprobe "$DEVICE"
        sleep 2
        
        # 由于有时 partprobe 不够可靠，尝试多种方法强制内核重新读取分区表
        blockdev --rereadpt "$DEVICE" 2>/dev/null || true
        
        # 等待设备文件出现
        log "等待设备文件出现..."
        for i in {1..10}; do
            if [ -b "$PERSIST_PARTITION" ]; then
                log "设备文件 $PERSIST_PARTITION 已出现"
                break
            fi
            log "等待设备文件出现，尝试 $i/10..."
            sleep 1
        done
        
        # 检查新分区是否创建成功
        if [ ! -b "$PERSIST_PARTITION" ]; then
            log "错误: 分区 $PERSIST_PARTITION 创建失败，尝试手动创建"
            
            # 尝试使用 sfdisk 作为替代方法
            log "尝试使用 sfdisk 创建分区..."
            if command -v sfdisk &> /dev/null; then
                echo ",$persist_start,," | sfdisk --append "$DEVICE"  # 从指定扇区到磁盘末尾
                sleep 2
                partprobe "$DEVICE"
                sleep 1
            else
                log "sfdisk 不可用，无法继续尝试创建分区"
                return 1
            fi
            
            # 再次检查分区是否存在
            if [ ! -b "$PERSIST_PARTITION" ]; then
                log "错误: 即使使用替代方法，分区 $PERSIST_PARTITION 仍未创建成功"
                return 1
            fi
        fi
        
        # 验证分区大小和位置
        local part_info=$(fdisk -l "$DEVICE" | grep "$PERSIST_PARTITION")
        local start_sector=$(echo "$part_info" | awk '{print $2}')
        local end_sector=$(echo "$part_info" | awk '{print $3}')
        local size=$(echo "$part_info" | awk '{print $5}')
        
        log "新创建的持久性分区 $PERSIST_PARTITION 起始扇区: $start_sector, 结束扇区: $end_sector, 大小: $size"
        
        # 格式化分区为ext4
        log "格式化持久性数据分区为ext4..."
        mkfs.ext4 -F "$PERSIST_PARTITION"
        
        if [ $? -ne 0 ]; then
            log "错误: 格式化分区失败"
            return 1
        fi
    else
        log "持久性数据分区已存在: $PERSIST_PARTITION，将直接使用现有数据"
        
        # 检查现有分区的大小和位置
        local part_info=$(fdisk -l "$DEVICE" | grep "$PERSIST_PARTITION")
        local start_sector=$(echo "$part_info" | awk '{print $2}')
        local end_sector=$(echo "$part_info" | awk '{print $3}')
        local size=$(echo "$part_info" | awk '{print $5}')
        
        log "现有持久性分区 $PERSIST_PARTITION 起始扇区: $start_sector, 结束扇区: $end_sector, 大小: $size"
    fi
    
    # 创建挂载点
    mkdir -p "$PERSIST_MOUNT"
    
    # 挂载持久性数据分区
    log "挂载持久性数据分区..."
    mount "$PERSIST_PARTITION" "$PERSIST_MOUNT"
    
    if [ $? -ne 0 ]; then
        log "错误: 挂载持久性数据分区失败"
        return 1
    fi
    
    log "持久性数据分区设置完成"
    return 0
}

# 保存配置数据到持久化分区
save_config_to_persist() {
    log "保存配置数据到持久性分区..."
    
    # 确保持久性分区中有配置目录
    mkdir -p "$PERSIST_MOUNT"
    
    # 配置数据存在则进行复制
    if [ -d "$CFG_DIR" ]; then
        # 复制当前配置到持久性分区
        log "复制 $CFG_DIR 的内容到持久性分区..."
        rsync -av "$CFG_DIR/" "$PERSIST_MOUNT/"  
        if [ $? -ne 0 ]; then
            log "警告: 配置数据保存可能不完整"
        else
            log "配置数据成功保存到持久性分区"
        fi
    fi
    
    return 0
}

# 清理持久性分区挂载
cleanup_persist_partition() {
    if mount | grep -q "$PERSIST_MOUNT"; then
        log "卸载持久性数据分区..."
        umount "$PERSIST_MOUNT"
        rmdir "$PERSIST_MOUNT" 2>/dev/null || true
    fi
}

# 处理 OTA 包文件（使用本地文件）
prepare_ota_package() {
    log "开始准备 OTA 包文件..."
    
    # 检查是否提供了本地文件路径
    if [ -z "$OTA_PACKAGE_PATH" ]; then
        log "错误: 需要通过环境变量 OTA_PACKAGE_PATH 提供本地OTA包路径"
        return 1
    fi
    
    log "使用本地OTA包: $OTA_PACKAGE_PATH"
    
    # 检查本地文件是否存在
    if [ ! -f "$OTA_PACKAGE_PATH" ]; then
        log "错误: 本地OTA包文件不存在: $OTA_PACKAGE_PATH"
        return 1
    fi
    
    # 检查文件大小
    local file_size_bytes=$(stat -c%s "$OTA_PACKAGE_PATH" 2>/dev/null || echo "0")
    if [ "$file_size_bytes" -lt 1048576 ]; then
        log "错误: 本地OTA包文件大小异常小 ($file_size_bytes 字节)"
        return 1
    fi
    
    local zip_file="$OTA_PACKAGE_PATH"
    log "使用OTA包文件: $zip_file"
    
    # 验证ZIP文件完整性
    log "测试ZIP文件完整性..."
    if ! unzip -t "$zip_file" >/dev/null 2>&1; then
        log "错误: ZIP文件完整性测试失败"
        return 1
    fi
    log "ZIP文件完整性测试通过"
    
    # 创建包目录并解压ZIP包
    local package_dir="$TMP_DIR/${ROOTFS_FILE_NAME}"
    log "创建包目录: $package_dir"
    mkdir -p "$package_dir"
    
    # 检查磁盘空间
    local available_space=$(df "$TMP_DIR" | tail -1 | awk '{print $4}')
    local zip_size_kb=$(du -k "$zip_file" | awk '{print $1}')
    local required_space=$((zip_size_kb * 3))  # 需要3倍ZIP大小的空间
    
    log "磁盘可用空间: ${available_space}KB, ZIP大小: ${zip_size_kb}KB, 预估需要: ${required_space}KB"
    
    if [ "$available_space" -lt "$required_space" ]; then
        log "警告: 磁盘空间可能不足，但将继续尝试解压"
    fi
    
    log "解压 OTA zip 包到包目录..."
    if ! unzip -q "$zip_file" -d "$package_dir"; then
        log "错误: OTA zip包解压失败"
        return 1
    fi
    
    log "OTA zip包解压完成"
    
    log "验证解压后的文件结构..."
    
    # 检查必要的文件是否存在
    if [ ! -f "$ROOTFS_XZ_FILE" ]; then
        log "错误: 解压后未找到rootfs压缩文件: $ROOTFS_XZ_FILE"
        log "正在列出解压目录内容进行诊断..."
        ls -la "$package_dir" | tee -a "$LOG_FILE"
        return 1
    fi
    
    log "OTA包文件准备完成"
    return 0
}

# 处理 rootfs 文件（解压和验证）
prepare_rootfs() {
    log "开始准备 rootfs 文件..."
    
    # 检查是否需要解压
    local need_extract=true
    local md5_file="${TMP_DIR}/${ROOTFS_FILE_NAME}/${ROOTFS_FILE_NAME}.ext4.xz.md5"
    
    # 验证压缩文件的MD5校验和
    if [ "$VERIFY_CHECKSUM" = true ] && [ -f "$md5_file" ]; then
        log "验证压缩文件的完整性..."
        
        if verify_md5 "$ROOTFS_XZ_FILE" "$md5_file"; then
            log "压缩文件校验通过"
        else
            log "错误: 压缩文件校验失败，终止升级过程"
            return 1
        fi
    fi
    
    # 检查是否需要解压
    local uncompressed_md5_file="${TMP_DIR}/${ROOTFS_FILE_NAME}/${ROOTFS_FILE_NAME}.ext4.md5"
    
    if [ -f "$ROOTFS_FILE" ]; then
        log "发现已存在的未压缩文件: $ROOTFS_FILE，将检查其完整性..."
        
        # 验证未压缩文件的MD5校验和
        if [ -f "$uncompressed_md5_file" ]; then
            if verify_md5 "$ROOTFS_FILE" "$uncompressed_md5_file"; then
                log "已存在的未压缩文件校验通过，将直接使用"
                need_extract=false
            else
                log "已存在的未压缩文件MD5校验失败，将重新解压"
                rm -f "$ROOTFS_FILE"
            fi
        else
            log "无法找到未压缩文件的MD5校验文件，将重新解压"
            rm -f "$ROOTFS_FILE"
        fi
    fi
    
    # 如果需要解压，则进行解压
    if [ "$need_extract" = true ]; then
        log "解压 rootfs 文件..."
        log "源文件: $ROOTFS_XZ_FILE"
        log "目标文件: $ROOTFS_FILE"
        
        # 使用xz解压，明确指定输出文件路径
        xz -d -k -v -c "$ROOTFS_XZ_FILE" > "$ROOTFS_FILE"
        
        if [ $? -ne 0 ]; then
            log "错误: rootfs 文件解压失败"
            return 1
        fi
        
        if [ ! -f "$ROOTFS_FILE" ]; then
            log "错误: 解压后的 rootfs 文件不存在"
            return 1
        fi
        
        # 验证解压后的文件完整性
        if [ "$VERIFY_CHECKSUM" = true ] && [ -f "$uncompressed_md5_file" ]; then
            log "验证解压后文件完整性..."
            
            if verify_md5 "$ROOTFS_FILE" "$uncompressed_md5_file"; then
                log "解压后的文件MD5校验通过"
            else
                log "错误: 解压后的文件MD5校验失败，终止升级过程"
                rm -f "$ROOTFS_FILE"
                return 1
            fi
        else
            log "警告: 无法找到未压缩文件的MD5校验文件，跳过MD5验证"
        fi
    fi
    
    log "rootfs 文件准备完成"
    
    # 处理 bmap 文件
    local package_dir="$TMP_DIR/${ROOTFS_FILE_NAME}"
    local bmap_file="${package_dir}/${ROOTFS_FILE_NAME}.ext4.bmap"
    
    # 检查bmap文件是否存在
    if [ -f "$bmap_file" ]; then
        log "发现bmap文件，复制到工作目录: $bmap_file"
        cp "$bmap_file" "$BMAP_FILE"
        
        # 验证bmap文件
        local bmap_md5_file="${package_dir}/${ROOTFS_FILE_NAME}.ext4.bmap.md5"
        if [ "$VERIFY_CHECKSUM" = true ] && [ -f "$bmap_md5_file" ]; then
            if verify_md5 "$BMAP_FILE" "$bmap_md5_file"; then
                log "bmap文件校验通过，将使用bmap以加速刷写"
                USE_BMAP=true
            else
                log "警告: bmap文件校验失败，将不使用bmap加速"
                USE_BMAP=false
                rm -f "$BMAP_FILE"
            fi
        else
            log "使用bmap文件加速刷写（未找到MD5校验文件）"
            USE_BMAP=true
        fi
    else
        log "未找到bmap文件，将使用普通模式刷写"
        USE_BMAP=false
    fi
    
    return 0
}

# 确定当前根分区并选择目标分区
detect_partitions() {
    log "检测当前根分区..."
    
    # 获取当前根分区
    CURRENT_ROOT_PARTITION=$(findmnt -n -o SOURCE /)
    
    if [ -z "$CURRENT_ROOT_PARTITION" ]; then
        log "错误: 无法确定当前根分区"
        exit 1
    fi
    
    log "当前根分区: $CURRENT_ROOT_PARTITION"
    
    # 根据当前分区决定目标分区
    if [ "$CURRENT_ROOT_PARTITION" = "$PARTITION_A" ]; then
        TARGET_PARTITION="$PARTITION_B"
        log "目标分区为 B 分区: $TARGET_PARTITION"
    elif [ "$CURRENT_ROOT_PARTITION" = "$PARTITION_B" ]; then
        TARGET_PARTITION="$PARTITION_A"
        log "目标分区为 A 分区: $TARGET_PARTITION"
    else
        log "错误: 当前根分区 $CURRENT_ROOT_PARTITION 不是预期的 A 或 B 分区"
        exit 1
    fi
}

# 创建或调整目标分区
prepare_target_partition() {
    log "准备目标分区: $TARGET_PARTITION..."
    
    # 检查目标分区是否为块设备
    if [ ! -b "$TARGET_PARTITION" ]; then
        log "目标分区 $TARGET_PARTITION 不存在，需要创建"
        
        # 获取磁盘总扇区数
        local total_sectors=$(fdisk -l "$DEVICE" | grep "sectors$" | awk '{print $7}')
        
        # 获取当前分区的结束扇区（无论是 p2 还是 p3）
        local current_end=$(fdisk -l "$DEVICE" | grep "$CURRENT_ROOT_PARTITION" | awk '{print $3}')
        
        # 计算新分区的开始扇区（当前分区结束扇区 + 1）
        local target_start=$((current_end + 1))
        
        # 确定分区号（2 或 3）
        local part_number=${TARGET_PARTITION: -1}
        
        # 检查这个分区号是否已存在，如果存在则删除
        if fdisk -l "$DEVICE" | grep -q "${DEVICE}p${part_number}"; then
            log "分区 ${part_number} 已存在，将删除并重新创建"
            echo -e "d\n${part_number}\nw" | fdisk "$DEVICE" > /dev/null 2>&1
            
            # 等待并刷新分区表
            sleep 1
            partprobe "$DEVICE"
            sleep 1
        fi
        
        # 使用 fdisk 创建新分区，大小为 3GB
        log "创建新的分区 ${part_number}，大小: 3GB..."
        echo -e "n\np\n${part_number}\n${target_start}\n+3G\nw" | fdisk "$DEVICE" > /dev/null 2>&1
        
        if [ $? -ne 0 ]; then
            log "错误: 创建分区 ${part_number} 失败"
            return 1
        fi
        
        # 刷新分区表
        log "刷新分区表..."
        partprobe "$DEVICE"
        sleep 2
        
        # 检查新分区是否创建成功
        if ! fdisk -l "$DEVICE" | grep -q "$TARGET_PARTITION"; then
            log "错误: 分区 $TARGET_PARTITION 创建失败"
            return 1
        fi
        
        # 验证分区大小
        local size_info=$(fdisk -l "$DEVICE" | grep "$TARGET_PARTITION" | awk '{print $4}')
        log "新创建的分区 $TARGET_PARTITION 大小: $size_info 扇区"
    else
        log "目标分区 $TARGET_PARTITION 已存在，将直接使用"
        
        # 检查现有分区的大小，如果不是3GB左右，则警告
        local size_gb=$(fdisk -l "$DEVICE" | grep "$TARGET_PARTITION" | awk '{print $5}')
        log "现有分区 $TARGET_PARTITION 大小: $size_gb"
        
        # 简单检查是否接近3GB（假设显示如"3G"）
        if [[ ! "$size_gb" =~ "3G" && ! "$size_gb" =~ "2.9G" && ! "$size_gb" =~ "3.0G" && ! "$size_gb" =~ "3.1G" ]]; then
            log "警告: 现有目标分区 $TARGET_PARTITION 大小可能不为3GB ($size_gb)，可能会影响OTA升级"
        fi
    fi
    
    log "目标分区准备完成"
    return 0
}

# 使用 bmaptool 写入 rootfs
write_rootfs() {
    log "开始将 rootfs 写入到目标分区 $TARGET_PARTITION..."
    
    # 检查分区是否存在
    if [ ! -b "$TARGET_PARTITION" ]; then
        log "错误: 目标分区不存在: $TARGET_PARTITION"
        return 1
    fi
    
    # 根据是否有 bmap 文件选择写入方式
    if [ "$USE_BMAP" = true ] && [ -f "$BMAP_FILE" ]; then
        log "使用 bmaptool 写入 rootfs (加速模式)..."
        bmaptool copy --bmap "$BMAP_FILE" "$ROOTFS_FILE" "$TARGET_PARTITION"
    else
        log "使用 bmaptool 写入 rootfs (普通模式)..."
        bmaptool copy "$ROOTFS_FILE" "$TARGET_PARTITION"
    fi
    
    if [ $? -ne 0 ]; then
        log "错误: 写入 rootfs 到 $TARGET_PARTITION 失败"
        return 1
    fi
    
    # 刷新缓冲区
    sync
    
    # 验证写入结果 - 通过挂载和检查文件系统
    log "验证写入结果并调整文件系统大小..."
    
    # 创建临时挂载点
    local TEMP_MOUNT="/tmp/verify_mount"
    mkdir -p "$TEMP_MOUNT"
    
    # 首先检测文件系统类型
    local fs_type=$(blkid -o value -s TYPE "$TARGET_PARTITION")
    if [ -z "$fs_type" ]; then
        log "警告: 无法检测目标分区的文件系统类型，假定为 ext4"
        fs_type="ext4"
    fi
    log "检测到文件系统类型: $fs_type"
    
    # 针对ext系列文件系统进行处理
    if [[ "$fs_type" == "ext"* ]]; then
        # 运行文件系统检查
        log "执行文件系统检查..."
        e2fsck -f -y "$TARGET_PARTITION"
        
        # 调整文件系统大小以填满整个分区
        log "调整文件系统大小以利用完整的 3GB 空间..."
        resize2fs "$TARGET_PARTITION"
        
        if [ $? -ne 0 ]; then
            log "警告: 文件系统调整大小可能失败，但将继续升级过程"
        else
            log "文件系统成功调整为最大尺寸"
        fi
    else
        log "警告: 不支持的文件系统类型 $fs_type，无法自动调整大小"
    fi
    
    # 尝试挂载分区进行验证
    if mount "$TARGET_PARTITION" "$TEMP_MOUNT" &>/dev/null; then
        # 检查可用空间
        df -h "$TEMP_MOUNT" | tee -a "$LOG_FILE"
        
        # 获取文件系统大小信息
        local fs_info=$(df -h "$TEMP_MOUNT" | tail -1)
        local total_size=$(echo "$fs_info" | awk '{print $2}')
        local used_size=$(echo "$fs_info" | awk '{print $3}')
        local avail_size=$(echo "$fs_info" | awk '{print $4}')
        
        log "文件系统信息 - 总大小: $total_size, 已用: $used_size, 可用: $avail_size"
        
        # 卸载分区
        umount "$TEMP_MOUNT"
        rm -rf "$TEMP_MOUNT"
        
        log "验证成功: 目标分区可以正常挂载，文件系统已调整大小"
    else
        log "错误: 无法挂载目标分区进行验证，写入可能失败"
        return 1
    fi
    
    log "rootfs 写入成功"
    return 0
}

# 更新启动配置 - 这将放在最后一步执行，只有在前面所有步骤都成功时才会执行
update_boot_config() {
    log "开始更新启动配置..."
    
    # 创建临时挂载点
    mkdir -p "$BOOT_MOUNT"
    
    # 挂载 boot 分区
    mount "$BOOT_PARTITION" "$BOOT_MOUNT"
    
    if [ $? -ne 0 ]; then
        log "错误: 无法挂载 boot 分区"
        return 1
    fi
    
    # 检查 uEnv.txt 是否存在
    if [ ! -f "$UENV_FILE" ]; then
        log "错误: uEnv.txt 文件不存在: $UENV_FILE"
        umount "$BOOT_MOUNT"
        return 1
    fi
    
    # 备份 uEnv.txt
    cp "$UENV_FILE" "${UENV_FILE}.bak"
    log "已备份 uEnv.txt 到 ${UENV_FILE}.bak"
    
    # 从目标分区路径提取分区号
    local target_part_number=${TARGET_PARTITION: -1}
    
    # 更新 root 参数
    log "更新启动参数以使用 $TARGET_PARTITION 作为根分区..."
    sed -i "s|root=root=/dev/mmcblk0p.|root=root=/dev/mmcblk0p${target_part_number}|g" "$UENV_FILE"
    
    # 验证更改
    if ! grep -q "root=root=$TARGET_PARTITION" "$UENV_FILE"; then
        log "错误: 更新 uEnv.txt 失败"
        cp "${UENV_FILE}.bak" "$UENV_FILE"
        umount "$BOOT_MOUNT"
        return 1
    fi
    
    # 卸载 boot 分区
    umount "$BOOT_MOUNT"
    
    log "启动配置更新成功，下次启动将使用 $TARGET_PARTITION 作为根分区"
    return 0
}

# 更新配置文件中的版本号
update_version_in_config() {
    if [ "$OTA_SUCCESS" = true ] && [ -n "$LATEST_VERSION" ]; then
        log "更新配置文件中的版本号为 $LATEST_VERSION..."
        
        # 检查配置文件是否存在
        if [ ! -f "$CONFIG_FILE" ]; then
            log "警告: 配置文件不存在: $CONFIG_FILE，无法更新版本号"
            return 1
        fi
        
        # 创建临时文件
        local TEMP_CONFIG="/tmp/config.json.tmp"
        
        # 使用jq更新版本号
        if command -v jq &> /dev/null; then
            jq ".version_id = \"$LATEST_VERSION\"" "$CONFIG_FILE" > "$TEMP_CONFIG"
            
            # 检查jq命令是否成功
            if [ $? -ne 0 ]; then
                log "错误: 使用jq更新版本号失败"
                return 1
            fi
            
            # 替换原始配置文件
            mv "$TEMP_CONFIG" "$CONFIG_FILE"
            log "配置文件中的版本号已成功更新为 $LATEST_VERSION"
        else
            log "警告: 未找到jq工具，尝试使用sed进行替换"
            
            # 使用sed备选方案
            sed -i "s/\"version_id\":[[:space:]]*\"[^\"]*\"/\"version_id\": \"$LATEST_VERSION\"/g" "$CONFIG_FILE"
            
            # 验证替换是否成功
            if grep -q "\"version_id\":[[:space:]]*\"$LATEST_VERSION\"" "$CONFIG_FILE"; then
                log "配置文件中的版本号已成功更新为 $LATEST_VERSION"
            else
                log "错误: 使用sed更新版本号失败"
                return 1
            fi
        fi
        
        return 0
    else
        log "OTA升级未完全成功或版本号未定义，不更新配置文件中的版本号"
        return 0
    fi
}

# 清理临时文件
cleanup() {
    log "清理临时文件..."
    
    # 删除下载的文件
    rm -f "$ROOTFS_XZ_FILE"
    rm -f "$ROOTFS_FILE"
    rm -f "$BMAP_FILE"
    
    # 只有在确认成功时才删除临时目录
    if [ "$1" = "success" ]; then
        rm -rf "$TMP_DIR"
    fi
    
    log "清理完成"
}

# 主函数
main() {
    log "======= OTA 升级开始 ======="
    
    # 检查 root 权限
    check_root
    
    # 准备环境
    setup_environment
    
    # 检查工具
    check_tools
    
    # 获取版本信息
    get_versions
    
    # 设置持久性数据分区
    if ! setup_persist_partition; then
        log "错误: 持久性数据分区设置失败，这是一个关键错误"
        log "为了保护系统配置数据，OTA 升级将被终止"
        cleanup "fail"
        exit 1
    fi
    
    # 保存配置数据到持久性分区
    save_config_to_persist
    
    # 准备 OTA 包文件（使用本地文件）
    if ! prepare_ota_package; then
        log "错误: 准备 OTA 包文件失败，终止升级"
        cleanup_persist_partition
        cleanup "fail"
        exit 1
    fi
    
    # 准备 rootfs 文件
    if ! prepare_rootfs; then
        log "错误: 准备 rootfs 文件失败，终止升级"
        cleanup_persist_partition
        cleanup "fail"
        exit 1
    fi
    
    # 检测当前根分区并选择目标分区
    detect_partitions
    
    # 准备目标分区
    if ! prepare_target_partition; then
        log "错误: 准备目标分区失败，终止升级"
        cleanup_persist_partition
        cleanup "fail"
        exit 1
    fi
    
    # 写入 rootfs
    if ! write_rootfs; then
        log "错误: 写入 rootfs 失败，终止升级"
        cleanup_persist_partition
        cleanup "fail"
        exit 1
    fi
    
    # 所有操作都成功，标记OTA升级成功
    OTA_SUCCESS=true
    
    # 仅在其他所有步骤都成功的情况下，更新启动配置
    if [ "$OTA_SUCCESS" = true ]; then
        log "所有OTA步骤都已成功执行，现在更新引导配置..."
        
        if ! update_boot_config; then
            log "错误: 启动配置更新失败，OTA升级未完成"
            OTA_SUCCESS=false
            cleanup_persist_partition
            cleanup "fail"
            exit 1
        fi
        
        # 更新配置文件中的版本号
        update_version_in_config
        
        log "======= OTA 升级完成 ======="
        log "系统将在下次启动时使用新的 rootfs 版本: $LATEST_VERSION"
        log "目标分区: $TARGET_PARTITION"
        log "配置数据已保存到持久性分区: $PERSIST_PARTITION"
        log "请使用 'reboot' 命令重启系统以应用更改"
        
        # 清理持久性分区挂载
        cleanup_persist_partition
        
        # 成功清理
        cleanup "success"
    else
        log "错误: OTA 步骤执行失败，系统将保持当前配置不变"
        cleanup_persist_partition
        cleanup "fail"
        exit 1
    fi
}

# 执行主函数
main
